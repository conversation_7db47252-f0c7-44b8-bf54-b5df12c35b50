#include "es_rule_engine.h"
#include <stdio.h>
#include <string.h>

#define TAG "VM_CORO_DEMO"

// 演示协程状态持久性的脚本
// 这个脚本模拟一个状态机，每次调用时执行一个状态，然后让出控制权
static const uint8_t state_machine_script[] = {
    // 状态机初始化
    VM_OP_PUSH8, 0,                    // 初始状态 = 0
    
    // 状态机循环开始 (地址: 2)
    VM_OP_DUP,                         // 复制当前状态
    VM_OP_PUSH8, 5,                    // 最大状态数
    VM_OP_LT,                          // 检查 state < 5
    VM_OP_JZ, 50,                      // 如果不小于5，跳转到结束
    
    // 根据当前状态执行不同操作
    VM_OP_DUP,                         // 复制状态用于比较
    
    // 状态0：初始化
    VM_OP_DUP,
    VM_OP_PUSH8, 0,
    VM_OP_EQ,
    VM_OP_JZ, 18,                      // 不是状态0，跳转检查下一状态
    VM_OP_PUSH8, 100,                  // 日志：状态0
    VM_OP_CALL, VM_SYSCALL_LOG,       // 调用日志（1个参数）
    VM_OP_POP,                         // 清理日志结果
    VM_OP_JMP, 41,                     // 跳转到状态递增
    
    // 状态1：数据采集 (地址: 18)
    VM_OP_DUP,
    VM_OP_PUSH8, 1,
    VM_OP_EQ,
    VM_OP_JZ, 28,                      // 不是状态1，跳转检查下一状态
    VM_OP_PUSH8, 1,                    // 获取事件1
    VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 调用获取事件（1个参数）
    VM_OP_PUSH8, 101,                  // 日志：状态1
    VM_OP_ADD,                         // 组合事件值到日志
    VM_OP_CALL, VM_SYSCALL_LOG,       // 调用日志（1个参数）
    VM_OP_POP,                         // 清理结果
    VM_OP_JMP, 41,                     // 跳转到状态递增
    
    // 状态2：数据处理 (地址: 28)
    VM_OP_DUP,
    VM_OP_PUSH8, 2,
    VM_OP_EQ,
    VM_OP_JZ, 36,                      // 不是状态2，跳转检查下一状态
    VM_OP_PUSH8, 102,                  // 日志：状态2
    VM_OP_CALL, VM_SYSCALL_LOG,       // 调用日志（1个参数）
    VM_OP_POP,                         // 清理日志结果
    VM_OP_JMP, 41,                     // 跳转到状态递增

    // 其他状态的默认处理 (地址: 36)
    VM_OP_PUSH8, 199,                  // 日志：其他状态基础值
    VM_OP_SWAP,                        // 交换状态值到栈顶
    VM_OP_ADD,                         // 组合状态值到日志
    VM_OP_CALL, VM_SYSCALL_LOG,       // 调用日志（1个参数）
    VM_OP_POP,                         // 清理日志结果

    // 状态递增 (地址: 41)
    VM_OP_PUSH8, 1,
    VM_OP_ADD,                         // state++

    // 延时500ms，然后让出控制权
    VM_OP_PUSH16, 244, 1,              // 压入500ms (小端序: 500 = 0x01F4)
    VM_OP_DELAY,                       // 延时500ms
    VM_OP_YIELD,                       // 让出控制权

    // 跳转回循环开始
    VM_OP_JMP, 2,

    // 状态机结束 (地址: 46)
    VM_OP_POP,                         // 清理状态值
    VM_OP_PUSH8, 255,                  // 返回完成标志
    VM_OP_RET
};

// 模拟系统接口
static uint32_t demo_get_event_value(uint16_t event_id) {
    static uint32_t counter = 0;
    counter += 10;
    return counter;  // 模拟递增的传感器值
}

static uint32_t demo_get_io_level(uint16_t pin) {
    return pin % 2;
}

static void demo_send_frame(const es_rule_frame* frame) {
    ES_PRINTF_I(TAG, "Frame sent: cmd=0x%04X", frame->cmd);
}

static uint32_t demo_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    switch (call_id) {
        case VM_SYSCALL_GET_EVENT:
            return demo_get_event_value((uint16_t)arg1);
        case VM_SYSCALL_GET_IO:
            return demo_get_io_level((uint16_t)arg1);
        case VM_SYSCALL_GET_TIME:
            return 12345;
        case VM_SYSCALL_LOG:
            switch (arg1) {
                case 100:
                    ES_PRINTF_I(TAG, "State Machine: Initializing");
                    break;
                case 101:
                    ES_PRINTF_I(TAG, "State Machine: Data Collection - Value: %u", arg2);
                    break;
                case 102:
                    ES_PRINTF_I(TAG, "State Machine: Data Processing");
                    break;
                case 199:
                    ES_PRINTF_I(TAG, "State Machine: State %u", arg2);
                    break;
                default:
                    ES_PRINTF_I(TAG, "VM Log: %u, %u, %u", arg1, arg2, arg3);
                    break;
            }
            return 0;
        default:
            return 0;
    }
}

// 创建持久的脚本表达式
es_rule_expr_node* create_persistent_state_machine(void) {
    // 分配表达式节点
    es_rule_expr_node* expr = malloc(sizeof(es_rule_expr_node));
    if (!expr) {
        ES_LOGE(TAG, "Failed to allocate expression node");
        return NULL;
    }
    
    // 分配字节码内存
    uint8_t* bytecode = malloc(sizeof(state_machine_script));
    if (!bytecode) {
        ES_LOGE(TAG, "Failed to allocate bytecode memory");
        free(expr);
        return NULL;
    }
    
    // 复制字节码
    memcpy(bytecode, state_machine_script, sizeof(state_machine_script));
    
    // 分配VM上下文
    vm_context_t* vm_ctx = malloc(sizeof(vm_context_t));
    if (!vm_ctx) {
        ES_LOGE(TAG, "Failed to allocate VM context");
        free(bytecode);
        free(expr);
        return NULL;
    }
    
    // 初始化VM
    if (vm_init(vm_ctx, bytecode, sizeof(state_machine_script)) < 0) {
        ES_LOGE(TAG, "Failed to initialize VM");
        free(vm_ctx);
        free(bytecode);
        free(expr);
        return NULL;
    }
    
    // 设置表达式
    expr->type = EXPR_SCRIPT;
    expr->data.script.bytecode_len = sizeof(state_machine_script);
    expr->data.script.bytecode = bytecode;
    expr->data.script.vm_ctx = vm_ctx;
    
    return expr;
}

// 释放脚本表达式资源
void free_script_expression(es_rule_expr_node* expr) {
    if (!expr || expr->type != EXPR_SCRIPT) {
        return;
    }
    
    if (expr->data.script.vm_ctx) {
        free(expr->data.script.vm_ctx);
    }
    if (expr->data.script.bytecode) {
        free((void*)expr->data.script.bytecode);
    }
    free(expr);
}

// 主演示函数
void es_rule_vm_coroutine_demo(void) {
    ES_PRINTF_I(TAG, "=== ES Rule Engine VM Coroutine Persistence Demo ===");
    
    // 设置系统接口
    es_rule_sys_if sys_if = {
        .get_event_value = demo_get_event_value,
        .get_io_level = demo_get_io_level,
        .send_frame = demo_send_frame,
        .vm_syscall = demo_vm_syscall
    };
    
    // 初始化规则引擎系统接口（模拟）
    s_rule_engine.sys_if = sys_if;
    
    // 创建持久的状态机脚本
    es_rule_expr_node* state_machine = create_persistent_state_machine();
    if (!state_machine) {
        ES_LOGE(TAG, "Failed to create state machine");
        return;
    }
    
    // 模拟规则引擎的多次调用
    es_rule_frame dummy_frame = {0};
    
    ES_PRINTF_I(TAG, "Starting state machine execution...");
    
    for (int cycle = 1; cycle <= 10; cycle++) {
        ES_PRINTF_I(TAG, "\n--- Execution Cycle %d ---", cycle);
        
        // 获取当前VM状态
        vm_state_t state = script_expr_get_state(state_machine);
        ES_PRINTF_I(TAG, "VM State before execution: %d", state);
        
        // 执行表达式（状态机会在每个状态后YIELD）
        uint32_t result = evaluate_expr_value(state_machine, &dummy_frame);
        
        // 获取执行后的状态
        state = script_expr_get_state(state_machine);
        ES_PRINTF_I(TAG, "VM State after execution: %d, Result: %u", state, result);
        
        // 检查是否完成
        if (script_expr_is_done(state_machine)) {
            ES_PRINTF_I(TAG, "State machine completed!");
            break;
        }
        
        // 模拟一些延迟
        // 在实际应用中，这里可能是其他任务的执行时间
    }
    
    // 演示重置功能
    ES_PRINTF_I(TAG, "\n--- Resetting State Machine ---");
    script_expr_reset(state_machine);
    
    // 再次执行几个周期
    for (int cycle = 1; cycle <= 3; cycle++) {
        ES_PRINTF_I(TAG, "\n--- Reset Cycle %d ---", cycle);
        uint32_t result = evaluate_expr_value(state_machine, &dummy_frame);
        vm_state_t state = script_expr_get_state(state_machine);
        ES_PRINTF_I(TAG, "Result: %u, VM State: %d", result, state);
    }
    
    // 清理资源
    free_script_expression(state_machine);
    
    ES_PRINTF_I(TAG, "\n=== Demo Completed ===");
}
