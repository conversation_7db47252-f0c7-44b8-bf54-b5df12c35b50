#include "es_rule_engine.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

#if USE_ES
#include "es.h"
#include "es_scheduler.h"
#include "es_log.h"
#include "es_drv_os.h"
#include "es_drv_can.h"
#endif

#define TAG "RULE"

// 规则状态和运行时状态现在定义在头文件中

// 内存池
#define MEM_POOL_SIZE (10 * 1024) // 10KB
static uint8_t mem_pool[MEM_POOL_SIZE] = {0};
static size_t mem_pool_offset = 0;

// 全局运行时状态数组已移除，现在直接嵌入在Frame结构体中

// 单例CANConfig实例
es_rule_engine s_rule_engine = {0};

static void process_rule(es_rule_frame* frame, uint32_t current_time);

// 4字节对齐内存分配
static void* mem_alloc(size_t size) {
    size_t aligned = (mem_pool_offset + 3) & ~3;
    if (aligned + size > MEM_POOL_SIZE) {
        ES_LOGE(TAG, "Memory allocation failed: requested %zu bytes, available %zu bytes", 
               size, MEM_POOL_SIZE - aligned);
        return NULL;
    }
    void* ptr = &mem_pool[aligned];
    mem_pool_offset = aligned + size;
    return ptr;
}

static inline uint32_t get_timestamp(void) {
    return es_os_get_tick_ms();
}

// 查找消息索引
static uint16_t find_message_index(uint16_t msg_id) {
    for (int i = 0; i < s_rule_engine.message_count; i++) {
        if (s_rule_engine.messages[i].id == msg_id) {
            return i;
        }
    }
    return 0xFFFF; // 无效索引
}

// 查找信号索引
static uint16_t find_signal_index(uint16_t message_index, uint16_t signal_id) {
    if (message_index >= s_rule_engine.message_count) {
        return 0xFFFF; // 无效索引
    }
    
    es_rule_message* message = &s_rule_engine.messages[message_index];
    for (int i = 0; i < message->signal_count; i++) {
        if (message->signals[i].id == signal_id) {
            return i;
        }
    }
    return 0xFFFF; // 无效索引
}



// 统一使用可变长度整数解码（带边界检查）
static uint32_t decode_varint(const uint8_t* data, uint32_t* offset, uint32_t max_len) {
    if (*offset >= max_len) {
        ES_LOGE(TAG, "decode_varint: offset %u >= max_len %u", *offset, max_len);
        return 0;
    }
    uint32_t value = 0;
    int shift = 0;
    uint8_t byte;
    do {
        if (*offset >= max_len) {
            ES_LOGE(TAG, "decode_varint: insufficient data for varint encoding");
            return value;
        }
        byte = data[(*offset)++];
        value |= (byte & 0x7F) << shift;
        shift += 7;
        if (shift >= 32) break;
    } while (byte & 0x80);
    return value;
}

// // 有符号变长整数解码（ZigZag解码）
// static int32_t decode_signed_varint(const uint8_t* data, uint32_t* offset, uint32_t max_len) {
//     uint32_t encoded = decode_varint(data, offset, max_len);
//     // ZigZag解码：(n >> 1) ^ (-(n & 1))
//     return (int32_t)((encoded >> 1) ^ (-(encoded & 1)));
// }

// 解码合并的类型和操作符
static void decode_type_operator_combined(const uint8_t* data, uint32_t* offset, uint32_t max_len,
                                         ExprType* expr_type, OperatorType* operator) {
    if (*offset >= max_len) {
        ES_LOGE(TAG, "decode_type_operator_combined: offset %u >= max_len %u", *offset, max_len);
        *expr_type = 0;
        *operator = 0;
        return;
    }

    uint8_t combined = data[(*offset)++];
    *expr_type = (combined >> 4) & 0x0F;  // 高4位
    *operator = combined & 0x0F;          // 低4位
}

// 解码信号字段信息（start_bit和len）
static void decode_signal_field(const uint8_t* data, uint32_t* offset, uint32_t max_len,
                                       uint32_t* start_bit, uint32_t* bit_length) {
    if (*offset >= max_len) {
        ES_LOGE(TAG, "decode_field: offset %u >= max_len %u", *offset, max_len);
        *start_bit = 0;
        *bit_length = 0;
        return;
    }

    uint8_t first_byte = data[*offset];

    // 检查编码格式
    if ((first_byte & 0x80) == 0) {
        // 1字节格式：0xxx xxxx
        *start_bit = (first_byte >> 3) & 0x0F;    // bit 6-3
        *bit_length = (first_byte & 0x07) + 1;    // bit 2-0 + 1
        (*offset)++;
        return;
    }
    else if ((first_byte & 0xC0) == 0x80) {
        // 2字节格式：10xx xxxx xxxx xxxx
        if (*offset + 1 >= max_len) {
            ES_LOGE(TAG, "decode_field: insufficient data for 2-byte field encoding");
            *start_bit = 0;
            *bit_length = 0;
            return;
        }

        // 读取2字节大端序数据
        uint16_t encoded = (data[*offset] << 8) | data[*offset + 1];
        *start_bit = (encoded >> 8) & 0x3F;      // bit 13-8
        *bit_length = (encoded >> 2) & 0x3F;     // bit 7-2
        *offset += 2;
        return;
    }
    else if ((first_byte & 0xC0) == 0xC0) {
        // varint格式：11xx xxxx
        (*offset)++; // 跳过标识字节
        *start_bit = decode_varint(data, offset, max_len);
        *bit_length = decode_varint(data, offset, max_len);
        return;
    }
    else {
        ES_LOGE(TAG, "decode_field: invalid field encoding format: 0x%02x", first_byte);
        *start_bit = 0;
        *bit_length = 0;
        return;
    }
}

// 验证操作符是否在对应表达式类型的有效范围内
static bool is_valid_operator_for_type(ExprType expr_type, OperatorType op) {
    switch (expr_type) {
        case EXPR_COMPARE:
            return op <= 5;  // 比较操作符：0-5 (==, !=, >, <, >=, <=)
        case EXPR_LOGIC:
            return op <= 2;  // 逻辑操作符：0-2 (and, or, not)
        case EXPR_ARITHMETIC:
            return op <= 3;  // 算术操作符：0-3 (+, -, *, /)
        case EXPR_BITWISE:
            return op <= 4;  // 位操作符：0-4 (<<, >>, &, |, ~)
        case EXPR_SCRIPT:
            return op == 0;  // 脚本节点不使用操作符
        default:
            return op == 0;  // 无操作符类型应该操作符为0
    }
}

// 新的表达式解析（支持合并的类型和操作符编码）
static es_rule_expr_node* decode_expr(const uint8_t* data, uint32_t* offset, uint32_t max_len) {
    // 边界检查
    if (*offset >= max_len) {
        ES_LOGE(TAG, "decode_expr: offset %u >= max_len %u", *offset, max_len);
        return NULL;
    }

    es_rule_expr_node* node = mem_alloc(sizeof(es_rule_expr_node));
    if (!node) {
        ES_LOGE(TAG, "decode_expr: memory allocation failed");
        return NULL;
    }

    // 解码合并的类型和操作符
    ExprType expr_type;
    OperatorType operator;
    decode_type_operator_combined(data, offset, max_len, &expr_type, &operator);

    if (*offset > max_len) {
        ES_LOGE(TAG, "decode_expr: failed to decode type/operator");
        return NULL;
    }

    // 验证操作符是否有效
    if (!is_valid_operator_for_type(expr_type, operator)) {
        ES_LOGE(TAG, "decode_expr: invalid operator %u for type %u", operator, expr_type);
        return NULL;
    }

    node->type = expr_type;

    switch (expr_type) {
        case EXPR_CONSTANT:
            node->data.constant.value = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode constant value");
                return NULL;
            }
            break;

        case EXPR_SIGNAL: {
            uint32_t signal_id = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode signal ID");
                return NULL;
            }
            // 查找消息索引
            uint16_t msg_idx = 0xFFFF;
            uint16_t sig_idx = 0xFFFF;
            
            for (int i = 0; i < s_rule_engine.message_count; i++) {
                for (int j = 0; j < s_rule_engine.messages[i].signal_count; j++) {
                    if (s_rule_engine.messages[i].signals[j].id == signal_id) {
                        msg_idx = i;
                        sig_idx = j;
                        break;
                    }
                }
                if (msg_idx != 0xFFFF) break;
            }
            
            if (msg_idx == 0xFFFF || sig_idx == 0xFFFF) {
                return NULL;
            }
            
            node->data.signal.msg_idx = msg_idx;
            node->data.signal.sig_idx = sig_idx;
            break;
        }

        case EXPR_EVENT: {
            uint32_t event_id = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode event ID");
                return NULL;
            }
            node->data.event.id = (uint16_t)event_id;
            break;
        }

        case EXPR_MESSAGE_TIME: {
            uint32_t msg_idx = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode message_time msg_id");
                return NULL;
            }
            
            node->data.message_time.msg_idx = msg_idx;
            break;
        }

        case EXPR_SYSTEM_TIME:
            // 系统时间不需要额外参数
            node->data.system_time.dummy = 0;
            break;

        case EXPR_IO: {
            uint32_t pin = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode IO pin");
                return NULL;
            }
            node->data.io.pin = (uint16_t)pin;
            break;
        }

        case EXPR_COMPARE:
            node->data.compare.op = operator;

            // 递归解析左右子树
            node->data.compare.left = decode_expr(data, offset, max_len);
            if (!node->data.compare.left || *offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to parse compare left operand");
                return NULL;
            }

            node->data.compare.right = decode_expr(data, offset, max_len);
            if (!node->data.compare.right || *offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to parse compare right operand");
                return NULL;
            }
            break;

        case EXPR_LOGIC:
            node->data.logic.op = operator;

            // 读取操作数数量
            node->data.logic.count = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode logic operand count");
                return NULL;
            }

            // 检查操作数数量是否合理
            if (node->data.logic.count == 0 || node->data.logic.count > 16) {
                ES_LOGE(TAG, "decode_expr: invalid logic operand count: %u", node->data.logic.count);
                return NULL;
            }

            node->data.logic.operands = mem_alloc(node->data.logic.count * sizeof(es_rule_expr_node*));
            if (!node->data.logic.operands) {
                ES_LOGE(TAG, "decode_expr: memory allocation failed for logic operands");
                return NULL;
            }

            // 递归解析所有操作数
            for (int i = 0; i < node->data.logic.count; i++) {
                node->data.logic.operands[i] = decode_expr(data, offset, max_len);
                if (!node->data.logic.operands[i] || *offset > max_len) {
                    ES_LOGE(TAG, "decode_expr: failed to parse logic operand %d", i);
                    return NULL;
                }
            }
            break;

        case EXPR_ARITHMETIC:
            node->data.arithmetic.op = operator;

            // 递归解析左右子树
            node->data.arithmetic.left = decode_expr(data, offset, max_len);
            if (!node->data.arithmetic.left || *offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to parse arithmetic left operand");
                return NULL;
            }

            node->data.arithmetic.right = decode_expr(data, offset, max_len);
            if (!node->data.arithmetic.right || *offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to parse arithmetic right operand");
                return NULL;
            }
            break;

        case EXPR_BITWISE:
            node->data.bitwise.op = operator;

            // 递归解析左操作数
            node->data.bitwise.left = decode_expr(data, offset, max_len);
            if (!node->data.bitwise.left || *offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to parse bitwise left operand");
                return NULL;
            }

            // 根据操作符判断是否需要右操作数，只有~操作符不需要右操作数
            if (operator != OP_BIT_NOT) {
                // 双目操作符：<<, >>, &, | 需要右操作数
                node->data.bitwise.right = decode_expr(data, offset, max_len);
                if (!node->data.bitwise.right || *offset > max_len) {
                    ES_LOGE(TAG, "decode_expr: failed to parse bitwise right operand");
                    return NULL;
                }
            } else {
                // 单目操作符：~ 不需要右操作数
                node->data.bitwise.right = NULL;
            }
            break;

        case EXPR_SCRIPT: {
            // 读取字节码长度
            uint32_t bytecode_len = decode_varint(data, offset, max_len);
            if (*offset > max_len) {
                ES_LOGE(TAG, "decode_expr: failed to decode script bytecode length");
                return NULL;
            }

            // 检查字节码长度是否合理
            if (bytecode_len == 0 || bytecode_len > 1024) {
                ES_LOGE(TAG, "decode_expr: invalid script bytecode length: %u", bytecode_len);
                return NULL;
            }

            // 检查是否有足够的数据
            if (*offset + bytecode_len > max_len) {
                ES_LOGE(TAG, "decode_expr: insufficient data for script bytecode");
                return NULL;
            }

            // 分配字节码内存
            uint8_t* bytecode = mem_alloc(bytecode_len);
            if (!bytecode) {
                ES_LOGE(TAG, "decode_expr: memory allocation failed for script bytecode");
                return NULL;
            }

            // 复制字节码数据
            memcpy(bytecode, data + *offset, bytecode_len);
            *offset += bytecode_len;

            node->data.script.bytecode_len = (uint16_t)bytecode_len;
            node->data.script.bytecode = bytecode;
            break;
        }

        default:
            ES_LOGE(TAG, "decode_expr: unknown expression type: %u", expr_type);
            return NULL;
    }

    return node;
}

/**
 * @brief 规则引擎处理协程任务
 * @param coro 协程上下文
 * @param ctx 协程上下文参数
 * @return 协程返回值
 */
#if USE_ES
static es_async_t es_rule_process_task(es_coro_t *coro, void *ctx)
{
    es_co_begin(coro);

    while (1) {
        es_co_yield;

        uint32_t current_time = get_timestamp();
        for (int f = 0; f < s_rule_engine.frame_count; f++) {
            process_rule(&s_rule_engine.frames[f], current_time);
        }
    }

    es_co_end;
}
#endif // USE_ES

static void es_rule_engine_recv(uint16_t can_id, const uint8_t* data, uint8_t length) {
    // 查找存储的消息
    uint16_t msg_idx = find_message_index(can_id);
    if (msg_idx == 0xFFFF) return;

    es_rule_message* msg = &s_rule_engine.messages[msg_idx];

    // 更新消息数据
    memcpy(msg->data, data, length);
    msg->length = length;
    msg->timestamp = get_timestamp();

}

// 递归打印表达式树
static void print_expr_tree(const es_rule_expr_node* expr, int depth) {
    if (!expr) {
        ES_PRINTF_I(TAG, "%*s(null)", depth * 2, "");
        return;
    }

    const char* indent = "";
    char indent_buf[32] = {0};
    if (depth > 0) {
        snprintf(indent_buf, sizeof(indent_buf), "%*s", depth * 2, "");
        indent = indent_buf;
    }

    switch (expr->type) {
        case EXPR_CONSTANT:
            ES_PRINTF_I(TAG, "%sConstant: %d", indent, expr->data.constant.value);
            break;

        case EXPR_SIGNAL:
            ES_PRINTF_I(TAG, "%sSignal: msg_idx=%u, sig_idx=%u", indent,
                   expr->data.signal.msg_idx, expr->data.signal.sig_idx);
            break;

        case EXPR_EVENT:
            ES_PRINTF_I(TAG, "%sEvent: id=%u", indent, expr->data.event.id);
            break;

        case EXPR_MESSAGE_TIME:
            ES_PRINTF_I(TAG, "%sMessageTime: msg_idx=%u", indent, expr->data.message_time.msg_idx);
            break;

        case EXPR_SYSTEM_TIME:
            ES_PRINTF_I(TAG, "%sSystemTime", indent);
            break;

        case EXPR_IO:
            ES_PRINTF_I(TAG, "%sIO: pin=%u", indent, expr->data.io.pin);
            break;

        case EXPR_COMPARE: {
            const char* op_str = "?";
            switch (expr->data.compare.op) {
                case OP_EQ: op_str = "=="; break;
                case OP_NE: op_str = "!="; break;
                case OP_GT: op_str = ">"; break;
                case OP_LT: op_str = "<"; break;
                case OP_GE: op_str = ">="; break;
                case OP_LE: op_str = "<="; break;
            }
            ES_PRINTF_I(TAG, "%sCompare: %s", indent, op_str);
            print_expr_tree(expr->data.compare.left, depth + 1);
            print_expr_tree(expr->data.compare.right, depth + 1);
            break;
        }

        case EXPR_LOGIC: {
            const char* op_str = "?";
            switch (expr->data.logic.op) {
                case OP_AND: op_str = "AND"; break;
                case OP_OR: op_str = "OR"; break;
                case OP_NOT: op_str = "NOT"; break;
            }
            ES_PRINTF_I(TAG, "%sLogic: %s (count=%u)", indent, op_str, expr->data.logic.count);
            for (int i = 0; i < expr->data.logic.count; i++) {
                print_expr_tree(expr->data.logic.operands[i], depth + 1);
            }
            break;
        }

        case EXPR_ARITHMETIC: {
            const char* op_str = "?";
            switch (expr->data.arithmetic.op) {
                case OP_ADD: op_str = "+"; break;
                case OP_SUB: op_str = "-"; break;
                case OP_MUL: op_str = "*"; break;
                case OP_DIV: op_str = "/"; break;
            }
            ES_PRINTF_I(TAG, "%sArithmetic: %s", indent, op_str);
            print_expr_tree(expr->data.arithmetic.left, depth + 1);
            print_expr_tree(expr->data.arithmetic.right, depth + 1);
            break;
        }

        case EXPR_BITWISE: {
            const char* op_str = "?";
            switch (expr->data.bitwise.op) {
                case OP_LSHIFT: op_str = "<<"; break;
                case OP_RSHIFT: op_str = ">>"; break;
                case OP_BIT_AND: op_str = "&"; break;
                case OP_BIT_OR: op_str = "|"; break;
                case OP_BIT_NOT: op_str = "~"; break;
            }
            ES_PRINTF_I(TAG, "%sBitwise: %s", indent, op_str);
            print_expr_tree(expr->data.bitwise.left, depth + 1);
            if (expr->data.bitwise.right) {
                print_expr_tree(expr->data.bitwise.right, depth + 1);
            }
            break;
        }

        case EXPR_SCRIPT:
            ES_PRINTF_I(TAG, "%sScript: bytecode_len=%u", indent,
                   expr->data.script.bytecode_len);
            break;

        default:
            ES_PRINTF_I(TAG, "%sUnknown type: %u", indent, expr->type);
            break;
    }
}

void es_rule_engine_dump(void) {
    ES_PRINTF_I(TAG, "=== Rule Engine Configuration Dump ===");

    // 显示基本信息
    ES_PRINTF_I(TAG, "Version: %u", s_rule_engine.version);
    ES_PRINTF_I(TAG, "Message Count: %u", s_rule_engine.message_count);
    ES_PRINTF_I(TAG, "Frame Count: %u", s_rule_engine.frame_count);

    // 显示消息配置
    ES_PRINTF_I(TAG, "--- Messages ---");
    for (int i = 0; i < s_rule_engine.message_count; i++) {
        es_rule_message* msg = &s_rule_engine.messages[i];
        ES_PRINTF_I(TAG, "Message[%d]: ID=0x%03X, signals=%u, len=%u, ts=%u",
               i, msg->id, msg->signal_count, msg->length, msg->timestamp);

        // 显示信号信息
        for (int j = 0; j < msg->signal_count; j++) {
            es_rule_signal* sig = &msg->signals[j];
            ES_PRINTF_I(TAG, "  Signal[%d]: ID=%u, start_bit=%u, len=%u",
                   j, sig->id, sig->start_bit, sig->len);
        }

        // 显示消息数据（十六进制）
        if (msg->length > 0) {
            ES_LOG_HEX(TAG, 8, msg->data, msg->length);
        }
    }

    // 显示帧配置和状态
    ES_PRINTF_I(TAG, "--- Frames ---");
    for (int i = 0; i < s_rule_engine.frame_count; i++) {
        es_rule_frame* frame = &s_rule_engine.frames[i];
        const char* state_str = "";

        switch (frame->runtime.state) {
            case RULE_INACTIVE: state_str = "INACTIVE"; break;
            case RULE_PENDING: state_str = "PENDING"; break;
            case RULE_ACTIVE: state_str = "ACTIVE"; break;
            default: state_str = "UNKNOWN"; break;
        }

        ES_PRINTF_I(TAG, "Frame[%d]: cmd=0x%04X, data_count=%u, interval=%u, delay=%u",
               i, frame->cmd, frame->data_count, frame->rule.interval, frame->rule.delay);
        ES_PRINTF_I(TAG, "  Runtime: state=%s, time_value=%u", state_str, frame->runtime.time_value);

        // 显示开始规则条件
        ES_PRINTF_I(TAG, "  Start Condition:");
        if (frame->rule.start_condition) {
            print_expr_tree(frame->rule.start_condition, 2);
        } else {
            ES_PRINTF_I(TAG, "    (null)");
        }

        // 显示数据项
        for (int j = 0; j < frame->data_count; j++) {
            es_rule_data_item* data_item = &frame->data[j];
            ES_PRINTF_I(TAG, "  Data[%d]: msg_idx=%u, signal_count=%u",
                   j, data_item->idx, data_item->signal_count);

            // 显示信号ID列表
            for (int k = 0; k < data_item->signal_count; k++) {
                ES_PRINTF_I(TAG, "    Signal ID[%d]: %u", k, data_item->signals[k]);
            }
        }
    }

    // 显示系统接口状态
    ES_PRINTF_I(TAG, "--- System Interface ---");
    ES_PRINTF_I(TAG, "send_frame: %s", s_rule_engine.sys_if.send_frame ? "OK" : "NULL");
    ES_PRINTF_I(TAG, "get_event_value: %s", s_rule_engine.sys_if.get_event_value ? "OK" : "NULL");
    ES_PRINTF_I(TAG, "get_io_level: %s", s_rule_engine.sys_if.get_io_level ? "OK" : "NULL");

    ES_PRINTF_I(TAG, "=== End of Rule Engine Dump ===");
}

int es_rule_engine_load(const uint8_t* data, uint32_t length) {
    ES_LOGI(TAG, "Starting new binary format parsing, data length: %u bytes", length);

    mem_pool_offset = 0;
    uint32_t offset = 0;

    if(s_rule_engine.sys_if.send_frame == NULL) {
        ES_LOGE(TAG, "send_frame function pointer is NULL");
        return -1;
    }
    if(s_rule_engine.sys_if.get_event_value == NULL) {
        ES_LOGE(TAG, "get_event_value function pointer is NULL");
        return -1;
    }
    if(s_rule_engine.sys_if.get_io_level == NULL) {
        ES_LOGE(TAG, "get_io_level function pointer is NULL");
        return -1;
    }

    // 解码版本号
    s_rule_engine.version = decode_varint(data, &offset, length);
    if (offset > length) {
        ES_LOGE(TAG, "Failed to decode version");
        return -1;
    }
    ES_LOGI(TAG, "Version: %u", s_rule_engine.version);

    // 解码messages数量
    s_rule_engine.message_count = decode_varint(data, &offset, length);
    if (offset > length) {
        ES_LOGE(TAG, "Failed to decode message count");
        return -1;
    }
    ES_LOGI(TAG, "Message count: %u", s_rule_engine.message_count);

    // 分配messages数组
    s_rule_engine.messages = mem_alloc(s_rule_engine.message_count * sizeof(es_rule_message));
    if (!s_rule_engine.messages && s_rule_engine.message_count > 0) {
        ES_LOGE(TAG, "Memory allocation failed for messages");
        return -1;
    }

    // 解析messages（使用差值编码）
    uint32_t current_id = 0;
    for (int i = 0; i < s_rule_engine.message_count; i++) {
        es_rule_message* message = &s_rule_engine.messages[i];

        // 读取差值编码的ID
        uint32_t diff = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode message %d ID diff", i);
            return -1;
        }
        current_id += diff;
        message->id = current_id;

        // 读取signals数量
        message->signal_count = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode message %d signal count", i);
            return -1;
        }

        // 分配signals数组
        message->signals = mem_alloc(message->signal_count * sizeof(es_rule_signal));
        if (!message->signals && message->signal_count > 0) {
            ES_LOGE(TAG, "Memory allocation failed for message %d signals", i);
            return -1;
        }

        // 解析每个signal
        for (int j = 0; j < message->signal_count; j++) {
            es_rule_signal* signal = &message->signals[j];

            // 读取signal ID
            signal->id = decode_varint(data, &offset, length);
            if (offset > length) {
                ES_LOGE(TAG, "Failed to decode signal %d ID", j);
                return -1;
            }

            // 使用变长编码解码字段信息（start_bit和len）
            uint32_t start_bit_temp, len_temp;
            decode_signal_field(data, &offset, length, &start_bit_temp, &len_temp);
            if (offset > length) {
                ES_LOGE(TAG, "Failed to decode signal %d field info", j);
                return -1;
            }
            signal->start_bit = (uint8_t)start_bit_temp;
            signal->len = (uint8_t)len_temp;
        }

        // 初始化消息数据
        memset(message->data, 0, 8);
        message->length = 0;
        message->timestamp = 0;

        ES_PRINTF_I(TAG, "Parsed message %d: CAN ID=%u, signals=%u", i, message->id, message->signal_count);
    }

    // 解码frames数量
    s_rule_engine.frame_count = decode_varint(data, &offset, length);
    if (offset > length) {
        ES_LOGE(TAG, "Failed to decode frame count");
        return -1;
    }
    ES_LOGI(TAG, "Frame count: %u", s_rule_engine.frame_count);

    // 分配frames数组
    s_rule_engine.frames = mem_alloc(s_rule_engine.frame_count * sizeof(es_rule_frame));
    if (!s_rule_engine.frames && s_rule_engine.frame_count > 0) {
        ES_LOGE(TAG, "Memory allocation failed for frames");
        return -1;
    }

    // 解析frames
    for (int i = 0; i < s_rule_engine.frame_count; i++) {
        es_rule_frame* frame = &s_rule_engine.frames[i];

        // 读取cmd
        frame->cmd = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode frame %d command", i);
            return -1;
        }

        // 读取data数量
        frame->data_count = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode frame %d data count", i);
            return -1;
        }

        // 分配data数组
        frame->data = mem_alloc(frame->data_count * sizeof(es_rule_data_item));
        if (!frame->data && frame->data_count > 0) {
            ES_LOGE(TAG, "Memory allocation failed for frame %d data items", i);
            return -1;
        }

        // 解析每个data项
        for (int j = 0; j < frame->data_count; j++) {
            es_rule_data_item* data_item = &frame->data[j];

            // 读取消息索引
            data_item->idx = decode_varint(data, &offset, length);
            if (offset > length) {
                ES_LOGE(TAG, "Failed to decode data item %d message index", j);
                return -1;
            }

            // 读取signals数量
            data_item->signal_count = decode_varint(data, &offset, length);
            if (offset > length) {
                ES_LOGE(TAG, "Failed to decode data item %d signal count", j);
                return -1;
            }

            // 分配signals索引数组
            data_item->signals = mem_alloc(data_item->signal_count * sizeof(uint16_t));
            if (!data_item->signals && data_item->signal_count > 0) {
                ES_LOGE(TAG, "Memory allocation failed for data item %d signals", j);
                return -1;
            }

            // 读取每个signal索引
            for (int k = 0; k < data_item->signal_count; k++) {
                data_item->signals[k] = decode_varint(data, &offset, length);
                if (offset > length) {
                    ES_LOGE(TAG, "Failed to decode signal index %d", k);
                    return -1;
                }
            }
        }

        // 解析rule
        es_rule_info* rule = &frame->rule;

        // 读取cycle (对应Python的cycle)
        rule->interval = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode frame %d rule cycle", i);
            return -1;
        }

        // 读取delay
        rule->delay = decode_varint(data, &offset, length);
        if (offset > length) {
            ES_LOGE(TAG, "Failed to decode frame %d rule delay", i);
            return -1;
        }

        // 读取start_cond表达式
        rule->start_condition = decode_expr(data, &offset, length);
        if (!rule->start_condition || offset > length) {
            ES_LOGE(TAG, "Failed to parse frame %d start condition", i);
            return -1;
        }

        // 初始化frame的运行时状态
        frame->runtime.state = RULE_INACTIVE;
        frame->runtime.time_value = 0;

        ES_LOGI(TAG, "Parsed frame %d: cmd=%u, data_count=%u, cycle=%u, delay=%u",
               i, frame->cmd, frame->data_count, rule->interval, rule->delay);
    }

    es_rule_engine_dump();

    return 0;
}

#if USE_ES
static void can_msg_event_handler(const es_event_t *event, void *ctx) {
    // 处理CAN消息事件
    if (event->data_len == sizeof(es_can_msg_t)) {
        es_can_msg_t *msg = (es_can_msg_t *)event->u.data;
        es_rule_engine_recv(msg->id, msg->data, msg->dlc);
    }
}
#endif // USE_ES

// 默认VM系统调用实现（用于扩展的自定义调用）
static uint32_t default_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    // 这个函数现在主要用于处理用户自定义的系统调用
    // 标准系统调用已经在VM_OP_CALL中直接处理

    switch (call_id) {
        // 用户可以在这里添加自定义的系统调用
        // 例如：
        // case 0x80: // 自定义调用1
        //     return custom_function1(arg1, arg2);
        // case 0x81: // 自定义调用2
        //     return custom_function2(arg1, arg2, arg3);

        default:
            ES_PRINTF_E(TAG, "VM: unknown custom syscall ID %u", call_id);
            break;
    }

    return 0;
}

// 主解析函数（修改为单例模式）
int es_rule_engine_init(const es_rule_sys_if* sys_if, const uint8_t* data, uint32_t length) {

    s_rule_engine.sys_if = *sys_if;

    // 如果没有提供VM系统调用接口，使用默认实现
    if (!s_rule_engine.sys_if.vm_syscall) {
        s_rule_engine.sys_if.vm_syscall = default_vm_syscall;
    }

    if(es_rule_engine_load(data, length) != 0) {
        ES_LOGE(TAG, "Failed to load rule engine configuration");
        return -1;
    }

    #if USE_ES

    s_rule_engine.process_task = (es_coro_task_t) {
        .name = "rule_process",    
        .func = es_rule_process_task,
        .ctx = &s_rule_engine,    
    };

    es_scheduler_task_add(es_scheduler_get_default(), &s_rule_engine.process_task);

    es_scheduler_event_subscriber_init(&s_rule_engine.can_msg_subscriber, EVENT_TYPE_CAN_MSG, 
                                       can_msg_event_handler, NULL);
    es_scheduler_event_subscribe(es_scheduler_get_default(), &s_rule_engine.can_msg_subscriber);

    #endif // USE_ES
    
    return 0;
}

// 从字节数组中提取指定位的值
static uint32_t extract_bits(const uint8_t* data, uint8_t start_bit, uint8_t bit_length) {
    if (bit_length == 0 || bit_length > 32) return 0;
    
    uint32_t result = 0;
    uint8_t current_bit = start_bit;
    
    for (int i = 0; i < bit_length; i++) {
        uint8_t byte_index = current_bit / 8;
        uint8_t bit_index = current_bit % 8;
        
        if (byte_index >= 8) break; // 超出CAN数据帧范围
        
        if (data[byte_index] & (1 << bit_index)) {
            result |= (1 << i);
        }
        
        current_bit++;
    }
    
    return result;
}

// 从stored_messages中获取信号值
static uint32_t get_signal_value_from_stored(uint16_t msg_idx, uint8_t start_bit, uint8_t bit_length) {
    // 在stored_messages链表中查找对应的CAN ID
    es_rule_message* msg = &s_rule_engine.messages[msg_idx];
    return extract_bits(msg->data, start_bit, bit_length);
}


static uint32_t get_signal_value_by_id(uint16_t signal_id, const es_rule_frame* frame) {
    // 在frame的data项中查找对应的信号
    for (int i = 0; i < frame->data_count; i++) {
        const es_rule_data_item* data_item = &frame->data[i];

        // 获取对应的message
        if (data_item->idx >= s_rule_engine.message_count) {
            continue; // 无效的消息索引
        }
        const es_rule_message* message = &s_rule_engine.messages[data_item->idx];

        // 在该消息的signals中查找
        for (int j = 0; j < data_item->signal_count; j++) {
            uint16_t signal_idx = data_item->signals[j];
            if (signal_idx >= message->signal_count) {
                continue; // 无效的信号索引
            }

            const es_rule_signal* signal = &message->signals[signal_idx];
            if (signal->id == signal_id) {
                // 找到匹配的信号，从stored_messages中获取信号值
                return get_signal_value_from_stored(data_item->idx, signal->start_bit, signal->len);
            }
        }
    }

    // 如果没有找到对应的信号，返回0
    return 0;
}

// 获取信号值
static uint32_t get_signal_value(uint16_t msg_idx, uint16_t sig_idx) {
    if (msg_idx >= s_rule_engine.message_count) {
        return 0;
    }
    
    es_rule_message* message = &s_rule_engine.messages[msg_idx];
    
    if (sig_idx >= message->signal_count) {
        return 0;
    }
    
    es_rule_signal* signal = &message->signals[sig_idx];
    return extract_bits(message->data, signal->start_bit, signal->len);
}

// 前向声明
static bool evaluate_expr(const es_rule_expr_node* expr, const es_rule_frame* frame);

// 表达式数值计算（用于算术运算）
static uint32_t evaluate_expr_value(const es_rule_expr_node* expr, const es_rule_frame* frame) {
    if (expr == NULL) return 0;
    
    switch (expr->type) {
        case EXPR_CONSTANT:
            return expr->data.constant.value;
            
        case EXPR_SIGNAL:
            return get_signal_value(expr->data.signal.msg_idx, expr->data.signal.sig_idx);
            
        case EXPR_EVENT:
            return s_rule_engine.sys_if.get_event_value(expr->data.event.id);

        case EXPR_MESSAGE_TIME:
            if (expr->data.message_time.msg_idx < s_rule_engine.message_count) {
                return s_rule_engine.messages[expr->data.message_time.msg_idx].timestamp;
            }
            return 0;

        case EXPR_SYSTEM_TIME:
            return get_timestamp();

        case EXPR_IO:
            return s_rule_engine.sys_if.get_io_level(expr->data.io.pin);

        case EXPR_ARITHMETIC: {
            uint32_t left_val = evaluate_expr_value(expr->data.arithmetic.left, frame);
            uint32_t right_val = evaluate_expr_value(expr->data.arithmetic.right, frame);

            switch (expr->data.arithmetic.op) {
                case OP_ADD: return left_val + right_val;
                case OP_SUB: return left_val - right_val;
                case OP_MUL: return left_val * right_val;
                case OP_DIV: return (right_val != 0) ? left_val / right_val : 0;
                default: return 0;
            }
        }

        case EXPR_BITWISE: {
            uint32_t left_val = evaluate_expr_value(expr->data.bitwise.left, frame);
            uint32_t right_val = expr->data.bitwise.right ? evaluate_expr_value(expr->data.bitwise.right, frame) : 0;

            switch (expr->data.bitwise.op) {
                case OP_LSHIFT: return left_val << right_val;
                case OP_RSHIFT: return left_val >> right_val;
                case OP_BIT_AND: return left_val & right_val;
                case OP_BIT_OR: return left_val | right_val;
                case OP_BIT_NOT: return ~left_val;
                default: return 0;
            }
        }

        case EXPR_COMPARE:
        case EXPR_LOGIC:
            // 对于比较和逻辑表达式，返回布尔值转换为数值
            return evaluate_expr(expr, frame) ? 1 : 0;

        case EXPR_SCRIPT: {
            // 执行脚本并返回结果（同步执行）
            vm_context_t vm_ctx;
            if (vm_init(&vm_ctx, expr->data.script.bytecode,
                       expr->data.script.bytecode_len) < 0) {
                ES_PRINTF_E(TAG, "Failed to initialize VM");
                return 0;
            }

            // 确保系统接口有VM调用支持
            es_rule_sys_if sys_if_copy = s_rule_engine.sys_if;
            if (!sys_if_copy.vm_syscall) {
                sys_if_copy.vm_syscall = default_vm_syscall;
            }

            // 同步执行VM直到完成
            vm_execute(&vm_ctx, &sys_if_copy);

            // 返回VM执行结果
            return vm_get_result(&vm_ctx);
        }

        default:
            return 0;
    }
}

// 表达式评估
static bool evaluate_expr(const es_rule_expr_node* expr, const es_rule_frame* frame) {
    if (expr == NULL) return false;
    
    switch (expr->type) {
        case EXPR_CONSTANT:
            return expr->data.constant.value != 0;
            
        case EXPR_SIGNAL: {
            return get_signal_value(expr->data.signal.msg_idx, expr->data.signal.sig_idx) != 0;
        }
            
        case EXPR_EVENT: {
            return s_rule_engine.sys_if.get_event_value(expr->data.event.id) != 0;
        }

        case EXPR_MESSAGE_TIME: {
            if (expr->data.message_time.msg_idx < s_rule_engine.message_count) {
                return s_rule_engine.messages[expr->data.message_time.msg_idx].timestamp != 0;
            }
            return false;
        }

        case EXPR_SYSTEM_TIME: {
            return get_timestamp() != 0;
        }

        case EXPR_IO: {
            return s_rule_engine.sys_if.get_io_level(expr->data.io.pin) != 0;
        }
            
        case EXPR_COMPARE: {
            // 使用新的数值计算函数
            uint32_t left_val = evaluate_expr_value(expr->data.compare.left, frame);
            uint32_t right_val = evaluate_expr_value(expr->data.compare.right, frame);

            // 执行比较操作
            switch (expr->data.compare.op) {
                case OP_EQ: return left_val == right_val;
                case OP_NE: return left_val != right_val;
                case OP_GT: return left_val > right_val;
                case OP_LT: return left_val < right_val;
                case OP_GE: return left_val >= right_val;
                case OP_LE: return left_val <= right_val;
                default: return false;
            }
        }
            
        case EXPR_ARITHMETIC: {
            // 算术表达式的结果转换为布尔值（非0为真）
            return evaluate_expr_value(expr, frame) != 0;
        }
            
        case EXPR_BITWISE: {
            // 位操作表达式的结果转换为布尔值（非0为真）
            return evaluate_expr_value(expr, frame) != 0;
        }
            
        case EXPR_LOGIC: {
            switch (expr->data.logic.op) {
                case OP_AND: {
                    for (int i = 0; i < expr->data.logic.count; i++) {
                        if (!evaluate_expr(expr->data.logic.operands[i], frame)) {
                            return false;
                        }
                    }
                    return true;
                }

                case OP_OR: {
                    for (int i = 0; i < expr->data.logic.count; i++) {
                        if (evaluate_expr(expr->data.logic.operands[i], frame)) {
                            return true;
                        }
                    }
                    return false;
                }

                case OP_NOT: {
                    if (expr->data.logic.count > 0) {
                        return !evaluate_expr(expr->data.logic.operands[0], frame);
                    }
                    return false;
                }

                default: return false;
            }
        }

        case EXPR_SCRIPT: {
            // 脚本表达式的结果转换为布尔值（非0为真）
            return evaluate_expr_value(expr, frame) != 0;
        }

        default:
            return false;
    }
}

// 处理单个规则
static void process_rule(es_rule_frame* frame, uint32_t current_time) {
    const es_rule_info* rule = &frame->rule;  
    es_rule_runtime* runtime = &frame->runtime;
    switch (runtime->state) {
        case RULE_INACTIVE:
            if (evaluate_expr(rule->start_condition, frame)) {
                if (rule->delay > 0) {
                    runtime->state = RULE_PENDING;
                    runtime->time_value = current_time + rule->delay;  // 在PENDING状态下作为activate_time使用
                    ES_LOGI(TAG, "Condition met, pending activation (delay=%dms)",  rule->delay);
                } else {
                    runtime->state = RULE_ACTIVE;
                    runtime->time_value = current_time;
                    s_rule_engine.sys_if.send_frame(frame);
                }
            }
            break;
            
        case RULE_PENDING:
            if (!evaluate_expr(rule->start_condition, frame)) {
                runtime->state = RULE_INACTIVE;
                ES_LOGI(TAG, "Condition no longer met, canceling pending activation");
                break;
            }
            if (current_time >= runtime->time_value) {
                runtime->state = RULE_ACTIVE;
                runtime->time_value = current_time;
                s_rule_engine.sys_if.send_frame(frame);
            }
            break;
            
        case RULE_ACTIVE:
            if (!evaluate_expr(rule->start_condition, frame)) {
                runtime->state = RULE_INACTIVE;
                ES_LOGI(TAG, "Start condition became false, deactivating immediate rule");
                break;
            }
                
            if (rule->interval != 0 && current_time - runtime->time_value >= rule->interval) {
                s_rule_engine.sys_if.send_frame(frame);
                runtime->time_value = current_time;
            }
            break;
    }
}

// ==================== VM实现 ====================

// VM初始化
int vm_init(vm_context_t* vm, const uint8_t* bytecode, uint16_t len) {
    if (!vm || !bytecode || len == 0) {
        return -1;
    }

    memset(vm, 0, sizeof(vm_context_t));
    vm->bytecode = bytecode;
    vm->bytecode_len = len;
    vm->state = VM_STATE_READY;
    vm->sp = 0;
    vm->pc = 0;

    return 0;
}

// VM重置
void vm_reset(vm_context_t* vm) {
    if (!vm) return;

    vm->sp = 0;
    vm->pc = 0;
    vm->state = VM_STATE_READY;
}

// 获取VM执行结果
uint32_t vm_get_result(vm_context_t* vm) {
    if (!vm) {
        ES_PRINTF_E(TAG, "VM context is NULL");
        return 0;
    }

    // 检查VM状态是否正常完成
    if (vm->state != VM_STATE_DONE) {
        ES_PRINTF_E(TAG, "VM not in DONE state, current state: %d", vm->state);
        return 0;
    }

    // 检查栈是否有结果
    if (vm->sp == 0) {
        ES_PRINTF_E(TAG, "VM stack is empty, no result available");
        return 0;
    }

    // 返回栈顶结果
    return vm->stack[vm->sp - 1];
}

// VM栈操作
static inline int vm_push(vm_context_t* vm, uint32_t value) {
    if (vm->sp >= VM_STACK_SIZE) {
        ES_PRINTF_E(TAG, "VM stack overflow");
        return -1;
    }
    vm->stack[vm->sp++] = value;
    return 0;
}

static inline uint32_t vm_pop(vm_context_t* vm) {
    if (vm->sp == 0) {
        ES_PRINTF_E(TAG, "VM stack underflow");
        return 0;
    }
    return vm->stack[--vm->sp];
}

static inline uint32_t vm_peek(vm_context_t* vm) {
    if (vm->sp == 0) {
        return 0;
    }
    return vm->stack[vm->sp - 1];
}

// 读取字节码中的数据
static uint8_t vm_read_u8(vm_context_t* vm) {
    if (vm->pc >= vm->bytecode_len) {
        ES_PRINTF_E(TAG, "VM PC out of bounds");
        return 0;
    }
    return vm->bytecode[vm->pc++];
}

static uint16_t vm_read_u16(vm_context_t* vm) {
    uint16_t value = vm_read_u8(vm);
    value |= (uint16_t)vm_read_u8(vm) << 8;
    return value;
}

static uint32_t vm_read_u32(vm_context_t* vm) {
    uint32_t value = vm_read_u16(vm);
    value |= (uint32_t)vm_read_u16(vm) << 16;
    return value;
}

// VM单步执行（基本操作）
int vm_step(vm_context_t* vm, const es_rule_sys_if* sys_if) {
    if (!vm || !sys_if) {
        return -1;
    }

    if (vm->state != VM_STATE_RUNNING && vm->state != VM_STATE_READY) {
        return 0;  // 不需要执行
    }

    if (vm->pc >= vm->bytecode_len) {
        vm->state = VM_STATE_DONE;
        return 0;
    }

    vm->state = VM_STATE_RUNNING;
    VMOpcode opcode = vm_read_u8(vm);

    switch (opcode) {
        case VM_OP_NOP:
            // 空操作
            break;

        case VM_OP_PUSH8: {
            uint8_t value = vm_read_u8(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_PUSH16: {
            uint16_t value = vm_read_u16(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_PUSH32: {
            uint32_t value = vm_read_u32(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_POP:
            vm_pop(vm);
            break;

        case VM_OP_DUP: {
            uint32_t value = vm_peek(vm);
            if (vm_push(vm, value) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_SWAP: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SWAP: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t a = vm_pop(vm);
            uint32_t b = vm_pop(vm);
            vm_push(vm, a);
            vm_push(vm, b);
            break;
        }

        case VM_OP_ADD: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM ADD: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a + b);
            break;
        }

        case VM_OP_SUB: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SUB: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a - b);
            break;
        }

        case VM_OP_MUL: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM MUL: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a * b);
            break;
        }

        case VM_OP_DIV: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM DIV: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            if (b == 0) {
                ES_PRINTF_E(TAG, "VM DIV: division by zero");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            vm_push(vm, a / b);
            break;
        }

        case VM_OP_MOD: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM MOD: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            if (b == 0) {
                ES_PRINTF_E(TAG, "VM MOD: division by zero");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            vm_push(vm, a % b);
            break;
        }

        case VM_OP_AND: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM AND: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a & b);
            break;
        }

        case VM_OP_OR: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM OR: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a | b);
            break;
        }

        case VM_OP_XOR: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM XOR: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a ^ b);
            break;
        }

        case VM_OP_NOT: {
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM NOT: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t a = vm_pop(vm);
            vm_push(vm, ~a);
            break;
        }

        case VM_OP_SHL: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SHL: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a << b);
            break;
        }

        case VM_OP_SHR: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM SHR: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, a >> b);
            break;
        }

        case VM_OP_EQ: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM EQ: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a == b) ? 1 : 0);
            break;
        }

        case VM_OP_NE: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM NE: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a != b) ? 1 : 0);
            break;
        }

        case VM_OP_LT: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM LT: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a < b) ? 1 : 0);
            break;
        }

        case VM_OP_LE: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM LE: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a <= b) ? 1 : 0);
            break;
        }

        case VM_OP_GT: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM GT: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a > b) ? 1 : 0);
            break;
        }

        case VM_OP_GE: {
            if (vm->sp < 2) {
                ES_PRINTF_E(TAG, "VM GE: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t b = vm_pop(vm);
            uint32_t a = vm_pop(vm);
            vm_push(vm, (a >= b) ? 1 : 0);
            break;
        }

        case VM_OP_JMP: {
            uint16_t addr = vm_read_u16(vm);
            if (addr >= vm->bytecode_len) {
                ES_PRINTF_E(TAG, "VM JMP: invalid address %u", addr);
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            vm->pc = addr;
            break;
        }

        case VM_OP_JZ: {
            uint16_t addr = vm_read_u16(vm);
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM JZ: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            if (value == 0) {
                if (addr >= vm->bytecode_len) {
                    ES_PRINTF_E(TAG, "VM JZ: invalid address %u", addr);
                    vm->state = VM_STATE_ERROR;
                    return -1;
                }
                vm->pc = addr;
            }
            break;
        }

        case VM_OP_JNZ: {
            uint16_t addr = vm_read_u16(vm);
            if (vm->sp < 1) {
                ES_PRINTF_E(TAG, "VM JNZ: insufficient stack elements");
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            uint32_t value = vm_pop(vm);
            if (value != 0) {
                if (addr >= vm->bytecode_len) {
                    ES_PRINTF_E(TAG, "VM JNZ: invalid address %u", addr);
                    vm->state = VM_STATE_ERROR;
                    return -1;
                }
                vm->pc = addr;
            }
            break;
        }

        case VM_OP_CALL: {
            uint8_t call_id = vm_read_u8(vm);
            uint32_t result = 0;

            // 根据系统调用ID确定参数个数并调用相应接口
            switch (call_id) {
                case VM_SYSCALL_GET_EVENT: {
                    if (vm->sp < 1) {
                        ES_PRINTF_E(TAG, "VM CALL GET_EVENT: insufficient arguments");
                        vm->state = VM_STATE_ERROR;
                        return -1;
                    }
                    uint16_t event_id = (uint16_t)vm_pop(vm);
                    if (sys_if->get_event_value) {
                        result = sys_if->get_event_value(event_id);
                    }
                    break;
                }

                case VM_SYSCALL_GET_IO: {
                    if (vm->sp < 1) {
                        ES_PRINTF_E(TAG, "VM CALL GET_IO: insufficient arguments");
                        vm->state = VM_STATE_ERROR;
                        return -1;
                    }
                    uint16_t pin = (uint16_t)vm_pop(vm);
                    if (sys_if->get_io_level) {
                        result = sys_if->get_io_level(pin);
                    }
                    break;
                }

                case VM_SYSCALL_GET_SIGNAL: {
                    if (vm->sp < 2) {
                        ES_PRINTF_E(TAG, "VM CALL GET_SIGNAL: insufficient arguments");
                        vm->state = VM_STATE_ERROR;
                        return -1;
                    }
                    uint16_t sig_idx = (uint16_t)vm_pop(vm);
                    uint16_t msg_idx = (uint16_t)vm_pop(vm);

                    if (msg_idx < s_rule_engine.message_count) {
                        es_rule_message* msg = &s_rule_engine.messages[msg_idx];
                        if (sig_idx < msg->signal_count) {
                            es_rule_signal* sig = &msg->signals[sig_idx];
                            result = extract_bits(msg->data, sig->start_bit, sig->len);
                        }
                    }
                    break;
                }

                case VM_SYSCALL_GET_TIME: {
                    // 无参数
                    result = get_timestamp();
                    break;
                }

                case VM_SYSCALL_LOG: {
                    if (vm->sp < 1) {
                        ES_PRINTF_E(TAG, "VM CALL LOG: insufficient arguments");
                        vm->state = VM_STATE_ERROR;
                        return -1;
                    }
                    uint32_t log_value = vm_pop(vm);
                    ES_PRINTF_I(TAG, "VM LOG: %u", log_value);
                    result = 0;
                    break;
                }

                case VM_SYSCALL_SEND_FRAME: {
                    // 这个调用比较特殊，需要frame指针，暂时只记录日志
                    ES_PRINTF_I(TAG, "VM SEND_FRAME called");
                    result = 0;
                    break;
                }

                default: {
                    // 使用通用的vm_syscall接口作为扩展机制
                    uint32_t arg3 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    uint32_t arg2 = (vm->sp > 0) ? vm_pop(vm) : 0;
                    uint32_t arg1 = (vm->sp > 0) ? vm_pop(vm) : 0;

                    if (sys_if->vm_syscall) {
                        result = sys_if->vm_syscall(call_id, arg1, arg2, arg3);
                    } else {
                        ES_PRINTF_E(TAG, "VM: unknown syscall ID %u", call_id);
                        vm->state = VM_STATE_ERROR;
                        return -1;
                    }
                    break;
                }
            }

            if (vm_push(vm, result) < 0) {
                vm->state = VM_STATE_ERROR;
                return -1;
            }
            break;
        }

        case VM_OP_RET:
            vm->state = VM_STATE_DONE;
            break;

        case VM_OP_HALT:
            vm->state = VM_STATE_DONE;
            break;

        default:
            ES_PRINTF_E(TAG, "VM: unknown opcode 0x%02X at PC=%u", opcode, vm->pc - 1);
            vm->state = VM_STATE_ERROR;
            return -1;
    }

    return 1;  // 继续执行
}

// VM执行函数（简化版）
int vm_execute(vm_context_t* vm, const es_rule_sys_if* sys_if) {
    if (!vm || !sys_if) {
        return -1;
    }

    // 如果VM已经完成或出错，直接返回
    if (vm->state == VM_STATE_DONE || vm->state == VM_STATE_ERROR) {
        return 0;
    }

    // 如果VM处于准备状态，开始执行
    if (vm->state == VM_STATE_READY) {
        vm->state = VM_STATE_RUNNING;
    }

    // 执行指令直到完成或错误
    int max_steps = 10000;  // 防止无限循环
    while (max_steps-- > 0 && vm->state == VM_STATE_RUNNING) {
        int result = vm_step(vm, sys_if);
        if (result < 0) {
            return result;  // 错误
        }

        if (vm->state != VM_STATE_RUNNING) {
            break;  // 完成或错误
        }
    }

    if (max_steps <= 0) {
        ES_PRINTF_E(TAG, "VM execution timeout");
        vm->state = VM_STATE_ERROR;
        return -1;
    }

    return (vm->state == VM_STATE_DONE) ? 0 : -1;
}







// ==================== 脚本表达式相关函数 ====================
// 脚本表达式现在使用同步执行，不需要状态管理函数

#if USE_ES
// CAN消息处理函数
int es_rule_engine_on_msg(const es_can_msg_t* msg) {
    if (!msg) {
        return -1;
    }

    es_rule_engine_recv(msg->id, msg->data, msg->dlc);
    return 0;
}
#endif // USE_ES

