#include "es_rule_engine.h"
#include <stdio.h>
#include <string.h>

#define TAG "VM_TEST"

// 测试用的系统接口实现
static uint32_t test_get_event_value(uint16_t event_id) {
    // 模拟事件值
    switch (event_id) {
        case 1: return 100;
        case 2: return 200;
        default: return 0;
    }
}

static uint32_t test_get_io_level(uint16_t pin) {
    // 模拟IO电平
    return (pin % 2) ? 1 : 0;
}

static void test_send_frame(const es_rule_frame* frame) {
    ES_PRINTF_I(TAG, "Frame sent: cmd=0x%04X", frame->cmd);
}

static uint32_t test_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    switch (call_id) {
        case VM_SYSCALL_GET_EVENT:
            return test_get_event_value((uint16_t)arg1);
        case VM_SYSCALL_GET_IO:
            return test_get_io_level((uint16_t)arg1);
        case VM_SYSCALL_LOG:
            ES_PRINTF_I(TAG, "VM LOG: %u, %u, %u", arg1, arg2, arg3);
            return 0;
        case VM_SYSCALL_GET_TIME:
            return 12345;  // 模拟时间戳
        default:
            return 0;
    }
}

// 创建测试字节码：计算 (10 + 20) * 2
static const uint8_t test_bytecode_arithmetic[] = {
    VM_OP_PUSH8, 10,        // 压入10
    VM_OP_PUSH8, 20,        // 压入20
    VM_OP_ADD,              // 加法：10 + 20 = 30
    VM_OP_PUSH8, 2,         // 压入2
    VM_OP_MUL,              // 乘法：30 * 2 = 60
    VM_OP_RET               // 返回
};

// 创建测试字节码：比较操作 (100 > 50)
static const uint8_t test_bytecode_compare[] = {
    VM_OP_PUSH8, 100,       // 压入100
    VM_OP_PUSH8, 50,        // 压入50
    VM_OP_GT,               // 大于比较：100 > 50 = 1
    VM_OP_RET               // 返回
};

// 创建测试字节码：系统调用
static const uint8_t test_bytecode_syscall[] = {
    VM_OP_PUSH8, 1,         // 压入事件ID 1
    VM_OP_CALL, VM_SYSCALL_GET_EVENT,  // 调用获取事件值（只需1个参数）
    VM_OP_PUSH8, 150,       // 压入150
    VM_OP_GT,               // 比较：event_value > 150
    VM_OP_RET               // 返回
};

// 创建测试字节码：协程操作
static const uint8_t test_bytecode_coroutine[] = {
    VM_OP_PUSH8, 123,       // 压入日志值
    VM_OP_CALL, VM_SYSCALL_LOG,  // 调用日志输出（只需1个参数）
    VM_OP_YIELD,            // 让出控制权
    VM_OP_PUSH8, 42,        // 压入42
    VM_OP_RET               // 返回
};

// 创建测试字节码：延时操作
static const uint8_t test_bytecode_delay[] = {
    VM_OP_PUSH8, 100,       // 压入日志值：开始
    VM_OP_CALL, VM_SYSCALL_LOG,  // 记录开始日志
    VM_OP_POP,              // 清理日志结果

    VM_OP_PUSH16, 100, 0,   // 压入延时时间：100ms (小端序)
    VM_OP_DELAY,            // 延时100ms

    VM_OP_PUSH8, 200,       // 压入日志值：延时完成
    VM_OP_CALL, VM_SYSCALL_LOG,  // 记录完成日志
    VM_OP_POP,              // 清理日志结果

    VM_OP_PUSH8, 99,        // 返回值
    VM_OP_RET               // 返回
};

void test_vm_basic_operations(void) {
    ES_PRINTF_I(TAG, "=== Testing VM Basic Operations ===");
    
    vm_context_t vm;
    es_rule_sys_if sys_if = {
        .get_event_value = test_get_event_value,
        .get_io_level = test_get_io_level,
        .send_frame = test_send_frame,
        .vm_syscall = test_vm_syscall
    };
    
    // 测试算术运算
    ES_PRINTF_I(TAG, "Testing arithmetic: (10 + 20) * 2");
    vm_init(&vm, test_bytecode_arithmetic, sizeof(test_bytecode_arithmetic));
    vm_execute(&vm, &sys_if);
    uint32_t result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "Result: %u (expected: 60)", result);
    
    // 测试比较运算
    ES_PRINTF_I(TAG, "Testing comparison: 100 > 50");
    vm_init(&vm, test_bytecode_compare, sizeof(test_bytecode_compare));
    vm_execute(&vm, &sys_if);
    result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "Result: %u (expected: 1)", result);
    
    // 测试系统调用
    ES_PRINTF_I(TAG, "Testing syscall: get_event(1) > 150");
    vm_init(&vm, test_bytecode_syscall, sizeof(test_bytecode_syscall));
    vm_execute(&vm, &sys_if);
    result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "Result: %u (expected: 0, since 100 <= 150)", result);
    
    // 测试协程操作
    ES_PRINTF_I(TAG, "Testing coroutine operations");
    vm_init(&vm, test_bytecode_coroutine, sizeof(test_bytecode_coroutine));
    
    // 第一次执行到yield
    int exec_result = vm_execute(&vm, &sys_if);
    ES_PRINTF_I(TAG, "First execution result: %d, VM state: %d", exec_result, vm.state);
    
    // 继续执行
    exec_result = vm_execute(&vm, &sys_if);
    result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "Final result: %u (expected: 42)", result);
}

void test_vm_coroutine_persistence(void) {
    ES_PRINTF_I(TAG, "=== Testing VM Coroutine State Persistence ===");

    // 创建一个包含协程操作的脚本表达式
    es_rule_expr_node script_expr;
    script_expr.type = EXPR_SCRIPT;
    script_expr.data.script.bytecode_len = sizeof(test_bytecode_coroutine);
    script_expr.data.script.bytecode = test_bytecode_coroutine;

    // 分配VM上下文（模拟解析过程）
    script_expr.data.script.vm_ctx = malloc(sizeof(vm_context_t));
    if (!script_expr.data.script.vm_ctx) {
        ES_LOGE(TAG, "Failed to allocate VM context");
        return;
    }

    // 初始化VM
    vm_init(script_expr.data.script.vm_ctx, test_bytecode_coroutine, sizeof(test_bytecode_coroutine));

    // 模拟frame参数
    es_rule_frame dummy_frame = {0};

    // 第一次调用 - 应该执行到YIELD
    ES_PRINTF_I(TAG, "First evaluation call...");
    uint32_t result1 = evaluate_expr_value(&script_expr, &dummy_frame);
    vm_state_t state1 = script_expr_get_state(&script_expr);
    ES_PRINTF_I(TAG, "First call result: %u, VM state: %d", result1, state1);

    // 第二次调用 - 应该从YIELD后继续执行
    ES_PRINTF_I(TAG, "Second evaluation call...");
    uint32_t result2 = evaluate_expr_value(&script_expr, &dummy_frame);
    vm_state_t state2 = script_expr_get_state(&script_expr);
    ES_PRINTF_I(TAG, "Second call result: %u, VM state: %d (expected: 42)", result2, state2);

    // 检查VM是否完成
    bool is_done = script_expr_is_done(&script_expr);
    ES_PRINTF_I(TAG, "VM execution completed: %s", is_done ? "Yes" : "No");

    // 重置VM并再次测试
    ES_PRINTF_I(TAG, "Resetting VM and testing again...");
    script_expr_reset(&script_expr);
    uint32_t result3 = evaluate_expr_value(&script_expr, &dummy_frame);
    vm_state_t state3 = script_expr_get_state(&script_expr);
    ES_PRINTF_I(TAG, "After reset - result: %u, VM state: %d", result3, state3);

    // 清理
    free(script_expr.data.script.vm_ctx);
}

void test_vm_in_expression(void) {
    ES_PRINTF_I(TAG, "=== Testing VM in Expression ===");

    // 创建一个包含脚本节点的表达式
    es_rule_expr_node script_expr;
    script_expr.type = EXPR_SCRIPT;
    script_expr.data.script.bytecode_len = sizeof(test_bytecode_arithmetic);
    script_expr.data.script.bytecode = test_bytecode_arithmetic;

    // 分配VM上下文（模拟解析过程）
    script_expr.data.script.vm_ctx = malloc(sizeof(vm_context_t));
    if (!script_expr.data.script.vm_ctx) {
        ES_LOGE(TAG, "Failed to allocate VM context");
        return;
    }

    // 初始化VM
    vm_init(script_expr.data.script.vm_ctx, test_bytecode_arithmetic, sizeof(test_bytecode_arithmetic));

    // 模拟frame参数
    es_rule_frame dummy_frame = {0};

    // 测试表达式求值
    uint32_t result = evaluate_expr_value(&script_expr, &dummy_frame);
    ES_PRINTF_I(TAG, "Script expression result: %u (expected: 60)", result);

    // 测试布尔求值
    bool bool_result = evaluate_expr(&script_expr, &dummy_frame);
    ES_PRINTF_I(TAG, "Script expression boolean: %d (expected: 1)", bool_result);

    // 清理
    free(script_expr.data.script.vm_ctx);
}

void test_vm_syscall_parameters(void) {
    ES_PRINTF_I(TAG, "=== Testing VM System Call Parameters ===");

    vm_context_t vm;
    es_rule_sys_if sys_if = {
        .get_event_value = test_get_event_value,
        .get_io_level = test_get_io_level,
        .send_frame = test_send_frame,
        .vm_syscall = test_vm_syscall
    };

    // 测试不同参数个数的系统调用

    // 1. 测试无参数调用：GET_TIME
    ES_PRINTF_I(TAG, "Testing GET_TIME (0 parameters)");
    const uint8_t bytecode_get_time[] = {
        VM_OP_CALL, VM_SYSCALL_GET_TIME,  // 无参数
        VM_OP_RET
    };
    vm_init(&vm, bytecode_get_time, sizeof(bytecode_get_time));
    vm_execute(&vm, &sys_if);
    uint32_t time_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "GET_TIME result: %u", time_result);

    // 2. 测试单参数调用：GET_EVENT
    ES_PRINTF_I(TAG, "Testing GET_EVENT (1 parameter)");
    const uint8_t bytecode_get_event[] = {
        VM_OP_PUSH8, 1,                   // 事件ID
        VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 1个参数
        VM_OP_RET
    };
    vm_init(&vm, bytecode_get_event, sizeof(bytecode_get_event));
    vm_execute(&vm, &sys_if);
    uint32_t event_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "GET_EVENT(1) result: %u (expected: 100)", event_result);

    // 3. 测试单参数调用：GET_IO
    ES_PRINTF_I(TAG, "Testing GET_IO (1 parameter)");
    const uint8_t bytecode_get_io[] = {
        VM_OP_PUSH8, 3,                   // IO引脚号
        VM_OP_CALL, VM_SYSCALL_GET_IO,   // 1个参数
        VM_OP_RET
    };
    vm_init(&vm, bytecode_get_io, sizeof(bytecode_get_io));
    vm_execute(&vm, &sys_if);
    uint32_t io_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "GET_IO(3) result: %u (expected: 1)", io_result);

    // 4. 测试双参数调用：GET_SIGNAL
    ES_PRINTF_I(TAG, "Testing GET_SIGNAL (2 parameters)");
    const uint8_t bytecode_get_signal[] = {
        VM_OP_PUSH8, 0,                   // 消息索引
        VM_OP_PUSH8, 0,                   // 信号索引
        VM_OP_CALL, VM_SYSCALL_GET_SIGNAL, // 2个参数
        VM_OP_RET
    };
    vm_init(&vm, bytecode_get_signal, sizeof(bytecode_get_signal));
    vm_execute(&vm, &sys_if);
    uint32_t signal_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "GET_SIGNAL(0,0) result: %u", signal_result);

    // 5. 测试单参数调用：LOG
    ES_PRINTF_I(TAG, "Testing LOG (1 parameter)");
    const uint8_t bytecode_log[] = {
        VM_OP_PUSH8, 255,                 // 日志值
        VM_OP_CALL, VM_SYSCALL_LOG,      // 1个参数
        VM_OP_RET
    };
    vm_init(&vm, bytecode_log, sizeof(bytecode_log));
    vm_execute(&vm, &sys_if);
    uint32_t log_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "LOG(255) result: %u", log_result);

    // 6. 测试组合调用
    ES_PRINTF_I(TAG, "Testing combined syscalls");
    const uint8_t bytecode_combined[] = {
        VM_OP_PUSH8, 1,                   // 事件ID 1
        VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 获取事件值
        VM_OP_DUP,                        // 复制结果
        VM_OP_CALL, VM_SYSCALL_LOG,      // 记录日志
        VM_OP_POP,                        // 清理日志结果
        VM_OP_PUSH8, 2,                   // 事件ID 2
        VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 获取另一个事件值
        VM_OP_ADD,                        // 相加
        VM_OP_RET
    };
    vm_init(&vm, bytecode_combined, sizeof(bytecode_combined));
    vm_execute(&vm, &sys_if);
    uint32_t combined_result = vm_get_result(&vm);
    ES_PRINTF_I(TAG, "Combined result: %u (expected: 300)", combined_result);
}

// 主测试函数
void es_rule_vm_test(void) {
    ES_PRINTF_I(TAG, "Starting ES Rule Engine VM Tests");

    test_vm_basic_operations();
    test_vm_syscall_parameters();
    test_vm_coroutine_persistence();
    test_vm_in_expression();

    ES_PRINTF_I(TAG, "ES Rule Engine VM Tests Completed");
}
