#include "es_rule_engine.h"
#include <stdio.h>
#include <string.h>

#define TAG "VM_DELAY_DEMO"

// 演示delay功能的脚本：LED闪烁控制器
// 这个脚本模拟一个LED闪烁控制器，使用delay实现精确的时间控制
static const uint8_t led_blink_script[] = {
    // 初始化：设置闪烁次数
    VM_OP_PUSH8, 5,                    // 闪烁5次
    
    // 闪烁循环开始 (地址: 2)
    VM_OP_DUP,                         // 复制剩余次数
    VM_OP_PUSH8, 0,                    // 检查是否为0
    VM_OP_EQ,
    VM_OP_JZ, 8,                       // 如果不为0，跳转到闪烁逻辑
    VM_OP_POP,                         // 清理计数器
    VM_OP_PUSH8, 255,                  // 返回完成标志
    VM_OP_RET,                         // 结束
    
    // 闪烁逻辑开始 (地址: 8)
    // LED开启
    VM_OP_PUSH8, 1,                    // IO引脚1
    VM_OP_CALL, VM_SYSCALL_GET_IO,    // 获取当前状态（模拟LED控制）
    VM_OP_PUSH8, 10,                   // 日志：LED ON
    VM_OP_ADD,                         // 组合状态到日志
    VM_OP_CALL, VM_SYSCALL_LOG,       // 记录LED开启
    VM_OP_POP,                         // 清理日志结果
    
    // 延时300ms（LED开启时间）
    VM_OP_PUSH16, 44, 1,               // 300ms (0x012C = 300)
    VM_OP_DELAY,                       // 延时300ms
    
    // LED关闭
    VM_OP_PUSH8, 20,                   // 日志：LED OFF
    VM_OP_CALL, VM_SYSCALL_LOG,       // 记录LED关闭
    VM_OP_POP,                         // 清理日志结果
    
    // 延时200ms（LED关闭时间）
    VM_OP_PUSH16, 200, 0,              // 200ms (0x00C8 = 200)
    VM_OP_DELAY,                       // 延时200ms
    
    // 减少闪烁次数
    VM_OP_PUSH8, 1,
    VM_OP_SUB,                         // count--
    
    // 跳转回循环开始
    VM_OP_JMP, 2,
};

// 演示复杂延时序列的脚本：交通灯控制器
static const uint8_t traffic_light_script[] = {
    // 交通灯状态：0=红灯, 1=绿灯, 2=黄灯
    VM_OP_PUSH8, 0,                    // 初始状态：红灯
    
    // 主循环开始 (地址: 2)
    VM_OP_DUP,                         // 复制当前状态
    
    // 状态0：红灯 (5秒)
    VM_OP_DUP,
    VM_OP_PUSH8, 0,
    VM_OP_EQ,
    VM_OP_JZ, 18,                      // 不是红灯，检查下一状态
    VM_OP_PUSH8, 100,                  // 日志：红灯
    VM_OP_CALL, VM_SYSCALL_LOG,
    VM_OP_POP,
    VM_OP_PUSH16, 136, 19,             // 5000ms (0x1388 = 5000)
    VM_OP_DELAY,                       // 延时5秒
    VM_OP_POP,                         // 清理状态
    VM_OP_PUSH8, 1,                    // 切换到绿灯
    VM_OP_JMP, 2,                      // 回到主循环
    
    // 状态1：绿灯 (8秒) (地址: 18)
    VM_OP_DUP,
    VM_OP_PUSH8, 1,
    VM_OP_EQ,
    VM_OP_JZ, 34,                      // 不是绿灯，检查下一状态
    VM_OP_PUSH8, 101,                  // 日志：绿灯
    VM_OP_CALL, VM_SYSCALL_LOG,
    VM_OP_POP,
    VM_OP_PUSH16, 64, 31,              // 8000ms (0x1F40 = 8000)
    VM_OP_DELAY,                       // 延时8秒
    VM_OP_POP,                         // 清理状态
    VM_OP_PUSH8, 2,                    // 切换到黄灯
    VM_OP_JMP, 2,                      // 回到主循环
    
    // 状态2：黄灯 (2秒) (地址: 34)
    VM_OP_PUSH8, 102,                  // 日志：黄灯
    VM_OP_CALL, VM_SYSCALL_LOG,
    VM_OP_POP,
    VM_OP_PUSH16, 208, 7,              // 2000ms (0x07D0 = 2000)
    VM_OP_DELAY,                       // 延时2秒
    VM_OP_POP,                         // 清理状态
    VM_OP_PUSH8, 0,                    // 切换回红灯
    VM_OP_JMP, 2,                      // 回到主循环
};

// 模拟系统接口
static uint32_t delay_demo_get_event_value(uint16_t event_id) {
    return event_id * 10;  // 简单的模拟值
}

static uint32_t delay_demo_get_io_level(uint16_t pin) {
    static uint32_t led_state = 0;
    led_state = !led_state;  // 模拟LED状态切换
    return led_state;
}

static void delay_demo_send_frame(const es_rule_frame* frame) {
    ES_PRINTF_I(TAG, "Frame sent: cmd=0x%04X", frame->cmd);
}

static uint32_t delay_demo_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    switch (call_id) {
        case VM_SYSCALL_GET_EVENT:
            return delay_demo_get_event_value((uint16_t)arg1);
        case VM_SYSCALL_GET_IO:
            return delay_demo_get_io_level((uint16_t)arg1);
        case VM_SYSCALL_GET_TIME:
            return es_os_get_tick_ms();
        case VM_SYSCALL_LOG:
            switch (arg1) {
                case 10:
                case 11:
                    ES_PRINTF_I(TAG, "LED: ON (state=%u)", arg1 - 10);
                    break;
                case 20:
                    ES_PRINTF_I(TAG, "LED: OFF");
                    break;
                case 100:
                    ES_PRINTF_I(TAG, "Traffic Light: RED");
                    break;
                case 101:
                    ES_PRINTF_I(TAG, "Traffic Light: GREEN");
                    break;
                case 102:
                    ES_PRINTF_I(TAG, "Traffic Light: YELLOW");
                    break;
                default:
                    ES_PRINTF_I(TAG, "VM Log: %u", arg1);
                    break;
            }
            return 0;
        default:
            return 0;
    }
}

// 创建LED闪烁脚本表达式
es_rule_expr_node* create_led_blink_expression(void) {
    es_rule_expr_node* expr = malloc(sizeof(es_rule_expr_node));
    if (!expr) return NULL;
    
    uint8_t* bytecode = malloc(sizeof(led_blink_script));
    if (!bytecode) {
        free(expr);
        return NULL;
    }
    
    memcpy(bytecode, led_blink_script, sizeof(led_blink_script));
    
    vm_context_t* vm_ctx = malloc(sizeof(vm_context_t));
    if (!vm_ctx) {
        free(bytecode);
        free(expr);
        return NULL;
    }
    
    vm_init(vm_ctx, bytecode, sizeof(led_blink_script));
    
    expr->type = EXPR_SCRIPT;
    expr->data.script.bytecode_len = sizeof(led_blink_script);
    expr->data.script.bytecode = bytecode;
    expr->data.script.vm_ctx = vm_ctx;
    
    return expr;
}

// 创建交通灯脚本表达式
es_rule_expr_node* create_traffic_light_expression(void) {
    es_rule_expr_node* expr = malloc(sizeof(es_rule_expr_node));
    if (!expr) return NULL;
    
    uint8_t* bytecode = malloc(sizeof(traffic_light_script));
    if (!bytecode) {
        free(expr);
        return NULL;
    }
    
    memcpy(bytecode, traffic_light_script, sizeof(traffic_light_script));
    
    vm_context_t* vm_ctx = malloc(sizeof(vm_context_t));
    if (!vm_ctx) {
        free(bytecode);
        free(expr);
        return NULL;
    }
    
    vm_init(vm_ctx, bytecode, sizeof(traffic_light_script));
    
    expr->type = EXPR_SCRIPT;
    expr->data.script.bytecode_len = sizeof(traffic_light_script);
    expr->data.script.bytecode = bytecode;
    expr->data.script.vm_ctx = vm_ctx;
    
    return expr;
}

// 释放脚本表达式
void free_delay_script_expression(es_rule_expr_node* expr) {
    if (!expr || expr->type != EXPR_SCRIPT) return;
    
    if (expr->data.script.vm_ctx) {
        free(expr->data.script.vm_ctx);
    }
    if (expr->data.script.bytecode) {
        free((void*)expr->data.script.bytecode);
    }
    free(expr);
}

// 主演示函数
void es_rule_vm_delay_demo(void) {
    ES_PRINTF_I(TAG, "=== ES Rule Engine VM Delay Demo ===");
    
    // 设置系统接口
    es_rule_sys_if sys_if = {
        .get_event_value = delay_demo_get_event_value,
        .get_io_level = delay_demo_get_io_level,
        .send_frame = delay_demo_send_frame,
        .vm_syscall = delay_demo_vm_syscall
    };
    
    // 初始化规则引擎系统接口
    s_rule_engine.sys_if = sys_if;
    
    // 演示1：LED闪烁控制器
    ES_PRINTF_I(TAG, "\n--- LED Blink Controller Demo ---");
    es_rule_expr_node* led_expr = create_led_blink_expression();
    if (led_expr) {
        es_rule_frame dummy_frame = {0};
        
        for (int cycle = 1; cycle <= 15; cycle++) {
            ES_PRINTF_I(TAG, "LED Cycle %d", cycle);
            
            uint32_t result = evaluate_expr_value(led_expr, &dummy_frame);
            vm_state_t state = script_expr_get_state(led_expr);
            
            ES_PRINTF_I(TAG, "Result: %u, State: %d", result, state);
            
            if (script_expr_is_done(led_expr)) {
                ES_PRINTF_I(TAG, "LED blink sequence completed!");
                break;
            }
            
            // 模拟系统调度间隔
            es_os_delay_ms(50);
        }
        
        free_delay_script_expression(led_expr);
    }
    
    // 演示2：交通灯控制器（运行几个周期）
    ES_PRINTF_I(TAG, "\n--- Traffic Light Controller Demo ---");
    es_rule_expr_node* traffic_expr = create_traffic_light_expression();
    if (traffic_expr) {
        es_rule_frame dummy_frame = {0};
        
        for (int cycle = 1; cycle <= 10; cycle++) {
            ES_PRINTF_I(TAG, "Traffic Cycle %d", cycle);
            
            uint32_t result = evaluate_expr_value(traffic_expr, &dummy_frame);
            vm_state_t state = script_expr_get_state(traffic_expr);
            
            ES_PRINTF_I(TAG, "Result: %u, State: %d", result, state);
            
            // 模拟系统调度间隔
            es_os_delay_ms(100);
        }
        
        free_delay_script_expression(traffic_expr);
    }
    
    ES_PRINTF_I(TAG, "\n=== Delay Demo Completed ===");
}
