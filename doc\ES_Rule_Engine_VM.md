# ES规则引擎VM扩展文档

## 概述

ES规则引擎现在支持脚本节点，通过内置的简易虚拟机(VM)执行字节码，提供更灵活的规则表达能力。VM支持协程操作，可以与ES框架的协程系统无缝集成。

## 特性

- **字节码执行**: 支持自定义字节码指令集
- **协程支持**: 支持`YIELD`和`WAIT`操作，与ES协程系统集成
- **系统调用**: 可以调用规则引擎的系统接口
- **多种数据类型**: 支持8位、16位、32位操作数
- **丰富的指令集**: 包括算术、逻辑、比较、跳转等操作

## 指令集

### 栈操作指令
- `VM_OP_NOP` (0x00): 空操作
- `VM_OP_PUSH8` (0x01): 压入8位立即数
- `VM_OP_PUSH16` (0x02): 压入16位立即数
- `VM_OP_PUSH32` (0x03): 压入32位立即数
- `VM_OP_POP` (0x04): 弹出栈顶元素
- `VM_OP_DUP` (0x05): 复制栈顶元素
- `VM_OP_SWAP` (0x06): 交换栈顶两个元素

### 算术运算指令
- `VM_OP_ADD` (0x10): 加法
- `VM_OP_SUB` (0x11): 减法
- `VM_OP_MUL` (0x12): 乘法
- `VM_OP_DIV` (0x13): 除法
- `VM_OP_MOD` (0x14): 取模

### 位运算指令
- `VM_OP_AND` (0x15): 按位与
- `VM_OP_OR` (0x16): 按位或
- `VM_OP_XOR` (0x17): 按位异或
- `VM_OP_NOT` (0x18): 按位取反
- `VM_OP_SHL` (0x19): 左移
- `VM_OP_SHR` (0x1A): 右移

### 比较指令
- `VM_OP_EQ` (0x20): 等于
- `VM_OP_NE` (0x21): 不等于
- `VM_OP_LT` (0x22): 小于
- `VM_OP_LE` (0x23): 小于等于
- `VM_OP_GT` (0x24): 大于
- `VM_OP_GE` (0x25): 大于等于

### 控制流指令
- `VM_OP_JMP` (0x30): 无条件跳转
- `VM_OP_JZ` (0x31): 零跳转
- `VM_OP_JNZ` (0x32): 非零跳转

### 系统调用指令
- `VM_OP_CALL` (0x40): 调用系统接口
- `VM_OP_RET` (0x41): 返回
- `VM_OP_YIELD` (0x42): 协程让出
- `VM_OP_WAIT` (0x43): 协程等待
- `VM_OP_DELAY` (0x44): 协程延时
- `VM_OP_HALT` (0xFF): 停机

## 系统调用

VM可以通过`VM_OP_CALL`指令调用以下系统接口，**每个调用使用实际需要的参数个数**：

### 标准系统调用

| 调用ID | 名称 | 参数个数 | 参数说明 | 功能 |
|--------|------|----------|----------|------|
| `VM_SYSCALL_GET_EVENT` (0x01) | 获取事件值 | 1 | event_id | 获取指定事件的当前值 |
| `VM_SYSCALL_GET_IO` (0x02) | 获取IO电平 | 1 | pin | 获取指定IO引脚的电平 |
| `VM_SYSCALL_GET_SIGNAL` (0x04) | 获取信号值 | 2 | msg_idx, sig_idx | 获取指定消息中信号的值 |
| `VM_SYSCALL_GET_TIME` (0x05) | 获取时间戳 | 0 | 无 | 获取当前系统时间戳 |
| `VM_SYSCALL_LOG` (0x06) | 日志输出 | 1 | value | 输出日志信息 |
| `VM_SYSCALL_SEND_FRAME` (0x03) | 发送帧 | 0 | 无 | 发送当前帧（特殊处理） |

### 参数传递规则

- 参数通过VM栈传递，按照**从左到右**的顺序压栈
- VM会根据调用ID自动确定参数个数
- 多余的栈元素不会被消耗
- 参数不足会导致VM错误

## 使用示例

### 基本算术运算

```c
// 计算 (10 + 20) * 2
static const uint8_t bytecode_arithmetic[] = {
    VM_OP_PUSH8, 10,        // 压入10
    VM_OP_PUSH8, 20,        // 压入20
    VM_OP_ADD,              // 加法：10 + 20 = 30
    VM_OP_PUSH8, 2,         // 压入2
    VM_OP_MUL,              // 乘法：30 * 2 = 60
    VM_OP_RET               // 返回
};
```

### 条件判断

```c
// 判断事件值是否大于阈值
static const uint8_t bytecode_condition[] = {
    VM_OP_PUSH8, 1,         // 事件ID（1个参数）
    VM_OP_CALL, VM_SYSCALL_GET_EVENT,  // 获取事件值
    VM_OP_PUSH8, 100,       // 阈值
    VM_OP_GT,               // 比较
    VM_OP_RET               // 返回
};
```

### 多参数系统调用

```c
// 获取信号值并记录日志
static const uint8_t bytecode_signal[] = {
    VM_OP_PUSH8, 0,         // 消息索引
    VM_OP_PUSH8, 1,         // 信号索引（2个参数）
    VM_OP_CALL, VM_SYSCALL_GET_SIGNAL,  // 获取信号值
    VM_OP_DUP,              // 复制信号值
    VM_OP_CALL, VM_SYSCALL_LOG,         // 记录日志（1个参数）
    VM_OP_POP,              // 清理日志结果
    VM_OP_RET               // 返回信号值
};
```

### 协程操作

```c
// 协程示例：执行任务后让出控制权
static const uint8_t bytecode_coroutine[] = {
    VM_OP_PUSH8, 123,       // 日志值（1个参数）
    VM_OP_CALL, VM_SYSCALL_LOG,  // 输出日志
    VM_OP_YIELD,            // 让出控制权
    VM_OP_PUSH8, 42,        // 继续执行
    VM_OP_RET               // 返回
};
```

### 延时操作

```c
// 延时示例：LED闪烁控制
static const uint8_t bytecode_led_blink[] = {
    VM_OP_PUSH8, 10,        // 日志：LED开启
    VM_OP_CALL, VM_SYSCALL_LOG,
    VM_OP_POP,              // 清理日志结果

    VM_OP_PUSH16, 44, 1,    // 300ms延时 (0x012C = 300)
    VM_OP_DELAY,            // 延时300ms

    VM_OP_PUSH8, 20,        // 日志：LED关闭
    VM_OP_CALL, VM_SYSCALL_LOG,
    VM_OP_POP,              // 清理日志结果

    VM_OP_PUSH16, 200, 0,   // 200ms延时 (0x00C8 = 200)
    VM_OP_DELAY,            // 延时200ms

    VM_OP_RET               // 返回
};
```

### 无参数调用

```c
// 获取当前时间戳
static const uint8_t bytecode_timestamp[] = {
    VM_OP_CALL, VM_SYSCALL_GET_TIME,  // 无参数调用
    VM_OP_RET               // 返回时间戳
};
```

## 在规则表达式中使用

脚本节点可以作为规则表达式的一部分使用。**重要**：VM上下文在表达式解析时分配，在整个表达式生命周期内保持状态：

```c
// 脚本表达式节点结构（在解析时自动创建）
struct {
    uint16_t bytecode_len;      // 字节码长度
    const uint8_t* bytecode;    // 字节码数据指针
    vm_context_t* vm_ctx;       // VM上下文（持久保存协程状态）
} script;

// 在表达式求值中使用
uint32_t result = evaluate_expr_value(&script_expr, frame);
bool condition = evaluate_expr(&script_expr, frame);

// 检查和管理VM状态
vm_state_t state = script_expr_get_state(&script_expr);
bool is_done = script_expr_is_done(&script_expr);
script_expr_reset(&script_expr);  // 重置VM状态
```

## VM架构

### 数据栈
- 大小：16个32位元素
- 用于存储操作数和中间结果

### 调用栈
- 大小：4层调用深度
- 用于支持函数调用和返回

### 程序计数器(PC)
- 16位地址空间
- 指向当前执行的指令

### 状态管理
- `VM_STATE_READY`: 准备执行
- `VM_STATE_RUNNING`: 正在执行
- `VM_STATE_YIELD`: 已让出控制权
- `VM_STATE_WAIT`: 等待条件满足
- `VM_STATE_DELAY`: 延时等待
- `VM_STATE_DONE`: 执行完成
- `VM_STATE_ERROR`: 执行错误

## 协程集成

VM与ES协程系统无缝集成，**关键特性是状态持久性**：

1. **YIELD操作**: VM可以主动让出控制权，状态保存在vm_context_t中
2. **WAIT操作**: VM可以等待特定条件满足后继续执行
3. **DELAY操作**: VM可以精确延时指定毫秒数后继续执行
4. **状态持久性**: VM状态在表达式多次求值之间保持不变
5. **自动恢复**: 下次调用时从上次停止的位置继续执行

### 协程状态管理

```c
// 检查VM状态
vm_state_t state = script_expr_get_state(expr);
switch (state) {
    case VM_STATE_READY:    // 准备执行
    case VM_STATE_RUNNING:  // 正在执行
    case VM_STATE_YIELD:    // 已让出，等待下次调用
    case VM_STATE_WAIT:     // 等待条件满足
    case VM_STATE_DELAY:    // 延时等待
    case VM_STATE_DONE:     // 执行完成
    case VM_STATE_ERROR:    // 执行错误
}

// 重置VM到初始状态
script_expr_reset(expr);
```

## 扩展系统接口

可以通过扩展`es_rule_sys_if`结构来添加新的VM功能：

```c
typedef struct {
    uint32_t (*get_event_value)(uint16_t event_id);
    uint32_t (*get_io_level)(uint16_t pin);
    void (*send_frame)(const es_rule_frame* frame);
    // VM扩展接口
    uint32_t (*vm_syscall)(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3);
} es_rule_sys_if;
```

## 测试

提供了完整的测试套件：

### 基本功能测试
```c
void es_rule_vm_test(void);
```
包含以下测试用例：
- 基本算术运算
- 比较操作
- 系统调用
- 协程操作
- 表达式集成
- **协程状态持久性测试**

### 协程演示
```c
void es_rule_vm_coroutine_demo(void);
```
演示状态机脚本的协程状态持久性：
- 多状态状态机实现
- YIELD操作的状态保持
- 多次调用间的状态连续性
- VM重置和重新开始

## 注意事项

1. **内存管理**: VM使用规则引擎的内存池，字节码数据会被复制到内存池中
2. **执行限制**: 单次执行最多100条指令，防止无限循环
3. **栈溢出**: 注意栈深度限制，避免栈溢出
4. **错误处理**: VM执行错误会设置错误状态并停止执行
5. **协程安全**: VM状态在协程切换时需要正确管理

## 性能考虑

- VM执行开销相对较小，适合嵌入式环境
- 字节码紧凑，内存占用少
- 支持增量执行，不会阻塞系统
- 协程机制确保实时性要求
