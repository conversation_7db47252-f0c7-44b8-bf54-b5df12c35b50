#ifndef ES_RULE_ENGINE_H
#define ES_RULE_ENGINE_H

#include <stdint.h>
#include <stdbool.h>

#define USE_ES 1

#if USE_ES
#include "es_scheduler.h"
#include "es_log.h"
#include "es_drv_can.h"
#endif

// 前向声明，避免依赖外部头文件
typedef struct es_coro_task es_coro_task_t;
typedef struct es_event_subscriber es_event_subscriber_t;

// 设置一字节对齐
// #pragma pack(1)

// 表达式类型（与Python脚本保持一致）
typedef uint8_t ExprType;
#define EXPR_CONSTANT   0
#define EXPR_SIGNAL     1
#define EXPR_EVENT      2
#define EXPR_MESSAGE_TIME 3  // 原EXPR_RXTIME，对应Python的message_time
#define EXPR_SYSTEM_TIME  4  // 原EXPR_SYSTIME，对应Python的system_time
#define EXPR_IO         5
#define EXPR_COMPARE    6    // 原EXPR_COMPARISON，对应Python的compare
#define EXPR_LOGIC      7    // 原EXPR_LOGICAL，对应Python的logic
#define EXPR_ARITHMETIC 8
#define EXPR_BITWISE    9
#define EXPR_SCRIPT     10   // 脚本节点



// 操作符类型（与Python脚本保持一致）
typedef uint8_t OperatorType;

// 比较操作符 (0-5)
#define OP_EQ  0   // == (Python: '==': 0)
#define OP_NE  1   // != (Python: '!=': 1)
#define OP_GT  2   // >  (Python: '>': 2)
#define OP_LT  3   // <  (Python: '<': 3)
#define OP_GE  4   // >= (Python: '>=': 4)
#define OP_LE  5   // <= (Python: '<=': 5)

// 逻辑操作符 (0-2)
#define OP_AND 0   // and (Python: 'and': 0)
#define OP_OR  1   // or  (Python: 'or': 1)
#define OP_NOT 2   // not (Python: 'not': 2)

// 算术操作符 (0-3)
#define OP_ADD 0   // + (Python: '+': 0)
#define OP_SUB 1   // - (Python: '-': 1)
#define OP_MUL 2   // * (Python: '*': 2)
#define OP_DIV 3   // / (Python: '/': 3)

// 位操作符 (0-4)
#define OP_LSHIFT  0  // << (Python: '<<': 0)
#define OP_RSHIFT  1  // >> (Python: '>>': 1)
#define OP_BIT_AND 2  // &  (Python: '&': 2)
#define OP_BIT_OR  3  // |  (Python: '|': 3)
#define OP_BIT_NOT 4  // ~  (Python: '~': 4)

// VM操作码定义
typedef uint8_t VMOpcode;

// 8位操作码 (0x00-0x7F)
#define VM_OP_NOP       0x00  // 空操作
#define VM_OP_PUSH8     0x01  // 压入8位立即数
#define VM_OP_PUSH16    0x02  // 压入16位立即数
#define VM_OP_PUSH32    0x03  // 压入32位立即数
#define VM_OP_POP       0x04  // 弹出栈顶
#define VM_OP_DUP       0x05  // 复制栈顶
#define VM_OP_SWAP      0x06  // 交换栈顶两个元素
#define VM_OP_ADD       0x10  // 加法
#define VM_OP_SUB       0x11  // 减法
#define VM_OP_MUL       0x12  // 乘法
#define VM_OP_DIV       0x13  // 除法
#define VM_OP_MOD       0x14  // 取模
#define VM_OP_AND       0x15  // 按位与
#define VM_OP_OR        0x16  // 按位或
#define VM_OP_XOR       0x17  // 按位异或
#define VM_OP_NOT       0x18  // 按位取反
#define VM_OP_SHL       0x19  // 左移
#define VM_OP_SHR       0x1A  // 右移
#define VM_OP_EQ        0x20  // 等于
#define VM_OP_NE        0x21  // 不等于
#define VM_OP_LT        0x22  // 小于
#define VM_OP_LE        0x23  // 小于等于
#define VM_OP_GT        0x24  // 大于
#define VM_OP_GE        0x25  // 大于等于
#define VM_OP_JMP       0x30  // 无条件跳转
#define VM_OP_JZ        0x31  // 零跳转
#define VM_OP_JNZ       0x32  // 非零跳转
#define VM_OP_CALL      0x40  // 调用系统接口
#define VM_OP_RET       0x41  // 返回
#define VM_OP_HALT      0xFF  // 停机

// VM栈大小定义
#define VM_STACK_SIZE   16    // VM栈深度

// VM状态
typedef enum {
    VM_STATE_READY = 0,    // 准备执行
    VM_STATE_RUNNING,      // 正在执行
    VM_STATE_DONE,         // 执行完成
    VM_STATE_ERROR         // 执行错误
} vm_state_t;

// VM上下文结构（同步执行模式）
typedef struct {
    uint32_t stack[VM_STACK_SIZE];  // 数据栈
    uint8_t sp;                     // 栈指针
    uint16_t pc;                    // 程序计数器
    vm_state_t state;               // VM状态
    const uint8_t* bytecode;        // 字节码指针
    uint16_t bytecode_len;          // 字节码长度
} vm_context_t;

// 规则状态
typedef uint8_t RuleState;
#define RULE_INACTIVE 0
#define RULE_PENDING 1
#define RULE_ACTIVE 2

// 表达式节点
typedef struct es_rule_expr_node {
    ExprType type;
    union {
        struct { int32_t value; } constant;      // 支持有符号值
        struct { uint16_t msg_idx; uint16_t sig_idx; } signal;
        struct { uint16_t id; } event;
        struct { uint16_t msg_idx; } message_time; // 获取指定消息的接收时间戳(ms)
        struct { uint8_t dummy; } system_time;    // 获取当前系统时间(ms)
        struct { uint16_t pin; } io;              // IO引脚号
        struct {
            OperatorType op;
            struct es_rule_expr_node *left;
            struct es_rule_expr_node *right;
        } compare;
        struct {
            OperatorType op;
            uint8_t count;
            struct es_rule_expr_node **operands;
        } logic;
        struct {
            OperatorType op;
            struct es_rule_expr_node *left;
            struct es_rule_expr_node *right;
        } arithmetic;
        struct {
            OperatorType op;
            struct es_rule_expr_node *left;
            struct es_rule_expr_node *right;  // 对于单目操作符如~，只使用left
        } bitwise;
        struct {
            uint16_t bytecode_len;      // 字节码长度
            const uint8_t* bytecode;    // 字节码数据指针
        } script;
    } data;
} es_rule_expr_node;



// 规则运行时状态
typedef struct {
    RuleState state;
    uint32_t time_value;  // PENDING状态下为activate_time，ACTIVE状态下为last_report_time
} es_rule_runtime;

// 规则配置 - 简化为直接包含3个字段
typedef struct {
    uint16_t interval;
    uint16_t delay;
    es_rule_expr_node* start_condition;
} es_rule_info;

// 信号定义
typedef struct {
    uint16_t id;
    uint8_t start_bit;
    uint8_t len;
} es_rule_signal;

// 消息定义
typedef struct {
    uint32_t id;  // CAN ID
    uint8_t data[8];         // 最大8字节
    uint32_t timestamp;
    uint8_t signal_count;
     uint8_t length;
    es_rule_signal* signals;
} es_rule_message;

// 数据项（frame中的data项）
typedef struct {
    uint16_t idx;  // 消息索引
    uint16_t* signals;  // 信号ID数组
    uint8_t signal_count;
} es_rule_data_item;

// 帧
typedef struct {
    uint16_t cmd;
    uint8_t data_count;
    es_rule_data_item* data;
    es_rule_info rule;
    es_rule_runtime runtime;
} es_rule_frame;

// VM系统调用ID定义
#define VM_SYSCALL_GET_EVENT    0x01  // 获取事件值
#define VM_SYSCALL_GET_IO       0x02  // 获取IO电平
#define VM_SYSCALL_SEND_FRAME   0x03  // 发送帧
#define VM_SYSCALL_GET_SIGNAL   0x04  // 获取信号值
#define VM_SYSCALL_GET_TIME     0x05  // 获取时间戳
#define VM_SYSCALL_LOG          0x06  // 日志输出

// 系统接口
typedef struct {
    uint32_t (*get_event_value)(uint16_t event_id);
    uint32_t (*get_io_level)(uint16_t pin);
    void (*send_frame)(const es_rule_frame* frame);
    // VM扩展接口
    uint32_t (*vm_syscall)(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3);
} es_rule_sys_if;

// 规则引擎配置
typedef struct {
    uint32_t version;
    es_rule_message* messages;
    uint8_t frame_count;
    uint8_t message_count;
    es_rule_frame* frames;
    es_rule_sys_if sys_if;
    #if USE_ES  
    es_coro_task_t process_task;
    es_event_subscriber_t can_msg_subscriber;
    #endif // USE_ES    
} es_rule_engine;

// 单例规则引擎实例
extern es_rule_engine s_rule_engine;

int es_rule_engine_init(const es_rule_sys_if* sys_if, const uint8_t* data, uint32_t length);
int es_rule_engine_load(const uint8_t* data, uint32_t length);
#if USE_ES
int es_rule_engine_on_msg(const es_can_msg_t* msg);
#endif // USE_ES
void es_rule_engine_dump(void);

// VM相关函数声明
int vm_init(vm_context_t* vm, const uint8_t* bytecode, uint16_t len);
int vm_step(vm_context_t* vm, const es_rule_sys_if* sys_if);
int vm_execute(vm_context_t* vm, const es_rule_sys_if* sys_if);
int vm_execute_sync(vm_context_t* vm, const es_rule_sys_if* sys_if);
void vm_reset(vm_context_t* vm);
uint32_t vm_get_result(vm_context_t* vm);

// 脚本表达式相关函数（同步执行，无状态管理）

// VM测试函数
void es_rule_vm_test(void);

// VM协程演示函数
void es_rule_vm_coroutine_demo(void);

// VM延时演示函数
void es_rule_vm_delay_demo(void);

// 恢复默认对齐
// #pragma pack()

#endif // ES_RULE_ENGINE_H
