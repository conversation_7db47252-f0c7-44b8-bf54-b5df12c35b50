#include "es_rule_engine.h"
#include <stdio.h>
#include <string.h>

#define TAG "VM_EXAMPLE"

// 示例：智能温控系统的VM脚本

// 脚本1：温度阈值检查
// 功能：检查温度传感器值是否超过设定阈值
// 输入：温度传感器事件ID = 1，阈值 = 25度
// 输出：1表示超温，0表示正常
static const uint8_t temp_check_script[] = {
    // 获取温度传感器值
    VM_OP_PUSH8, 1,                    // 温度传感器事件ID
    VM_OP_CALL, VM_SYSCALL_GET_EVENT,  // 调用获取事件值（1个参数）

    // 与阈值比较
    VM_OP_PUSH8, 25,                   // 温度阈值25度
    VM_OP_GT,                          // 比较：temp > 25

    // 如果超温，记录日志
    VM_OP_DUP,                         // 复制比较结果
    VM_OP_JZ, 16,                      // 如果不超温，跳转到结束

    // 超温处理：记录日志
    VM_OP_PUSH8, 1,                    // 日志值：警告代码
    VM_OP_CALL, VM_SYSCALL_LOG,       // 输出日志（1个参数）
    VM_OP_POP,                         // 弹出日志调用结果

    // 结束
    VM_OP_RET                          // 返回比较结果
};

// 脚本2：风扇控制逻辑
// 功能：根据温度和湿度控制风扇转速
// 输入：温度事件ID=1，湿度事件ID=2
// 输出：风扇转速等级(0-3)
static const uint8_t fan_control_script[] = {
    // 获取温度值
    VM_OP_PUSH8, 1,                    // 温度传感器事件ID
    VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 调用获取事件值（1个参数）

    // 获取湿度值
    VM_OP_PUSH8, 2,                    // 湿度传感器事件ID
    VM_OP_CALL, VM_SYSCALL_GET_EVENT, // 调用获取事件值（1个参数）
    
    // 计算综合指数：temp + humidity/2
    VM_OP_PUSH8, 2,
    VM_OP_DIV,                         // humidity/2
    VM_OP_ADD,                         // temp + humidity/2
    
    // 根据综合指数确定风扇等级
    VM_OP_DUP,                         // 复制综合指数
    VM_OP_PUSH8, 40,
    VM_OP_GT,                          // > 40
    VM_OP_JZ, 35,                      // 不大于40，跳转检查下一级
    VM_OP_POP,                         // 清理栈
    VM_OP_PUSH8, 3,                    // 最高速
    VM_OP_JMP, 50,                     // 跳转到结束
    
    // 检查是否 > 30
    VM_OP_DUP,
    VM_OP_PUSH8, 30,
    VM_OP_GT,
    VM_OP_JZ, 45,                      // 不大于30，跳转检查下一级
    VM_OP_POP,
    VM_OP_PUSH8, 2,                    // 中高速
    VM_OP_JMP, 50,
    
    // 检查是否 > 20
    VM_OP_DUP,
    VM_OP_PUSH8, 20,
    VM_OP_GT,
    VM_OP_JZ, 49,                      // 不大于20，低速
    VM_OP_POP,
    VM_OP_PUSH8, 1,                    // 低速
    VM_OP_JMP, 50,
    
    // 停止
    VM_OP_POP,
    VM_OP_PUSH8, 0,                    // 停止
    
    // 结束，记录风扇等级
    VM_OP_DUP,                         // 复制风扇等级
    VM_OP_PUSH8, 100,                  // 日志值：风扇控制代码
    VM_OP_ADD,                         // 组合风扇等级到日志值
    VM_OP_CALL, VM_SYSCALL_LOG,       // 记录日志（1个参数）
    VM_OP_POP,                         // 清理日志结果
    
    VM_OP_RET                          // 返回风扇等级
};

// 脚本3：协程示例 - 定时检查系统状态
static const uint8_t system_monitor_script[] = {
    // 循环开始
    VM_OP_PUSH8, 0,                    // 循环计数器
    
    // 循环体开始 (地址: 2)
    VM_OP_DUP,                         // 复制计数器
    VM_OP_PUSH8, 10,
    VM_OP_LT,                          // 检查是否 < 10
    VM_OP_JZ, 30,                      // 如果不小于10，退出循环
    
    // 执行监控任务
    VM_OP_PUSH8, 1,                    // 系统状态检查
    VM_OP_PUSH8, 0, VM_OP_PUSH8, 0,
    VM_OP_CALL, VM_SYSCALL_GET_TIME,  // 获取当前时间
    VM_OP_PUSH8, 200,                  // 日志类型：系统监控
    VM_OP_PUSH8, 0,
    VM_OP_CALL, VM_SYSCALL_LOG,       // 记录监控日志
    VM_OP_POP,                         // 清理结果
    
    // 让出控制权，允许其他任务执行
    VM_OP_YIELD,
    
    // 增加计数器
    VM_OP_PUSH8, 1,
    VM_OP_ADD,                         // counter++
    
    // 跳转到循环开始
    VM_OP_JMP, 2,
    
    // 循环结束 (地址: 30)
    VM_OP_POP,                         // 清理计数器
    VM_OP_PUSH8, 1,                    // 返回成功
    VM_OP_RET
};

// 创建包含VM脚本的规则表达式
es_rule_expr_node* create_temp_check_expression(void) {
    es_rule_expr_node* expr = malloc(sizeof(es_rule_expr_node));
    if (!expr) return NULL;
    
    expr->type = EXPR_SCRIPT;
    expr->data.script.bytecode_len = sizeof(temp_check_script);
    expr->data.script.bytecode = temp_check_script;
    
    return expr;
}

es_rule_expr_node* create_fan_control_expression(void) {
    es_rule_expr_node* expr = malloc(sizeof(es_rule_expr_node));
    if (!expr) return NULL;
    
    expr->type = EXPR_SCRIPT;
    expr->data.script.bytecode_len = sizeof(fan_control_script);
    expr->data.script.bytecode = fan_control_script;
    
    return expr;
}

// 示例系统接口实现
static uint32_t example_get_event_value(uint16_t event_id) {
    switch (event_id) {
        case 1: return 28;  // 温度28度
        case 2: return 65;  // 湿度65%
        default: return 0;
    }
}

static uint32_t example_get_io_level(uint16_t pin) {
    return (pin % 2);  // 模拟IO状态
}

static void example_send_frame(const es_rule_frame* frame) {
    ES_PRINTF_I(TAG, "Sending frame: cmd=0x%04X", frame->cmd);
}

static uint32_t example_vm_syscall(uint8_t call_id, uint32_t arg1, uint32_t arg2, uint32_t arg3) {
    switch (call_id) {
        case VM_SYSCALL_GET_EVENT:
            return example_get_event_value((uint16_t)arg1);
        case VM_SYSCALL_GET_IO:
            return example_get_io_level((uint16_t)arg1);
        case VM_SYSCALL_GET_TIME:
            return 12345;  // 模拟时间戳
        case VM_SYSCALL_LOG:
            ES_PRINTF_I(TAG, "VM Log - Type:%u, Value:%u, Extra:%u", arg1, arg2, arg3);
            return 0;
        default:
            return 0;
    }
}

// 主示例函数
void es_rule_vm_example(void) {
    ES_PRINTF_I(TAG, "=== ES Rule Engine VM Example ===");
    
    // 设置系统接口
    es_rule_sys_if sys_if = {
        .get_event_value = example_get_event_value,
        .get_io_level = example_get_io_level,
        .send_frame = example_send_frame,
        .vm_syscall = example_vm_syscall
    };
    
    // 测试温度检查脚本
    ES_PRINTF_I(TAG, "Testing temperature check script...");
    es_rule_expr_node* temp_expr = create_temp_check_expression();
    if (temp_expr) {
        es_rule_frame dummy_frame = {0};
        uint32_t result = evaluate_expr_value(temp_expr, &dummy_frame);
        ES_PRINTF_I(TAG, "Temperature check result: %u (1=over temp, 0=normal)", result);
        free(temp_expr);
    }
    
    // 测试风扇控制脚本
    ES_PRINTF_I(TAG, "Testing fan control script...");
    es_rule_expr_node* fan_expr = create_fan_control_expression();
    if (fan_expr) {
        es_rule_frame dummy_frame = {0};
        uint32_t fan_level = evaluate_expr_value(fan_expr, &dummy_frame);
        ES_PRINTF_I(TAG, "Fan control level: %u (0=stop, 1=low, 2=medium, 3=high)", fan_level);
        free(fan_expr);
    }
    
    // 测试协程监控脚本
    ES_PRINTF_I(TAG, "Testing system monitor coroutine script...");
    vm_context_t monitor_vm;
    vm_init(&monitor_vm, system_monitor_script, sizeof(system_monitor_script));
    
    // 模拟协程执行
    for (int i = 0; i < 5; i++) {
        ES_PRINTF_I(TAG, "Monitor execution cycle %d", i + 1);
        int result = vm_execute(&monitor_vm, &sys_if);
        ES_PRINTF_I(TAG, "VM state: %d, execution result: %d", monitor_vm.state, result);
        
        if (monitor_vm.state == VM_STATE_DONE) {
            ES_PRINTF_I(TAG, "Monitor script completed");
            break;
        }
    }
    
    ES_PRINTF_I(TAG, "=== VM Example Completed ===");
}
